#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    ~~~~~~~~~~~~~~~~~~~~~~~

    Description of this file

    :author: l<PERSON><PERSON><PERSON>
    :copyright: (c) dwm_enterprise_recruiting_second_dimension.py, Tungee
    :date created: 2022/12/20
    :python version: 3.6

"""
from tdr.common.constant.common import FormatType
from tdr.warehouse.ods.ods_certificate.meta.ods_certificate import OdsCertificate
from tdr.warehouse.ods.ods_recruiting.meta.ods_recruiting import OdsRecruiting


class DwmEnterpriseRecruitingSecondDimension(object):
    NAME = 'dwm_enterprise_recruiting_second_dimension'

    FORMAT_TYPE = FormatType.json

    class Field:
        _id = '_id'
        nameId = 'nameId'
        recruitingExists = "recruitingExists"
        valid_recruiting_count = 'valid_recruiting_count'
        recruiting_source = 'recruitingSource'
        recruiting_sources = 'recruitingSources'
        recruiting_current_no = 'recruitingCurrentNo'
        recruiting_current_count = 'recruitingCurrentCount'
        recruiting_last_three_month_no = 'recruitingLastThreeMonthNo'
        recruiting_last_three_month_count = 'recruitingLastThreeMonthCount'
        recruiting_name = 'recruitingName'
        recruiting_name_last_month = 'recruitingNameLastMonth'
        benefit_list = 'benefitList'
        recruiting_avg_work_salary = 'recruitingAvgWorkingSalary'
        recruiting_provinces = 'recruitingProvinces'
        recruiting_cities = 'recruitingCities'
        recruiting_avg_update = 'recruitingAvgUpdate'
        recruiting_types = 'recruitingTypes'
        name_md5 = 'nameMd5'
        real_recruiting_count = 'real_recruiting_count'
        recruitingExpiredNo = 'recruitingExpiredNo'
        has_recruiting = 'hasRecruiting'  # 当前有无招聘
        recruiting_stats = 'recruitingStats'
        recruiting_source_count = 'recruitingSourceCount'
        latest_recruiting_date = 'latestRecruitingDate'
        recruiting_last_half_year = 'recruitingLastHalfYear'  # 近半年各月招聘人数分布

        tab_stats = 'tabStats'

    class TabStatsType:
        recruiting = 'recruiting'
        operating_info_recruiting = 'operating_info::recruiting'

    # 输入
    INPUTS = [OdsRecruiting, OdsCertificate]

    # Spark Schema
    SPARK_SCHEMA = {
        "fields": [
            {
                "metadata": {

                },
                "name": "_id",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "benefitList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "latestRecruitingDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "nameId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "recruitingLastHalfYear",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "cnt",
                                "nullable": True,
                                "type": "long"
                            },
                            {
                                "metadata": {

                                },
                                "name": "date",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "recruitingSourceCount",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {

                },
                "name": "recruitingStats",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "hasRecruiting",
                                "nullable": True,
                                "type": "long"
                            },
                            {
                                "metadata": {

                                },
                                "name": "latestRecruitingDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "recruitingAvgUpdate",
                                "nullable": True,
                                "type": "long"
                            },
                            {
                                "metadata": {

                                },
                                "name": "recruitingAvgWorkingSalary",
                                "nullable": True,
                                "type": "double"
                            },
                            {
                                "metadata": {

                                },
                                "name": "recruitingCities",
                                "nullable": True,
                                "type": {
                                    "containsNull": True,
                                    "elementType": "string",
                                    "type": "array"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "recruitingCurrentCount",
                                "nullable": True,
                                "type": "double"
                            },
                            {
                                "metadata": {

                                },
                                "name": "recruitingCurrentNo",
                                "nullable": True,
                                "type": "long"
                            },
                            {
                                "metadata": {

                                },
                                "name": "recruitingExpiredNo",
                                "nullable": True,
                                "type": "long"
                            },
                            {
                                "metadata": {

                                },
                                "name": "recruitingLastThreeMonthCount",
                                "nullable": True,
                                "type": "double"
                            },
                            {
                                "metadata": {

                                },
                                "name": "recruitingLastThreeMonthNo",
                                "nullable": True,
                                "type": "long"
                            },
                            {
                                "metadata": {

                                },
                                "name": "recruitingName",
                                "nullable": True,
                                "type": {
                                    "containsNull": True,
                                    "elementType": "string",
                                    "type": "array"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "recruitingNameLastMonth",
                                "nullable": True,
                                "type": {
                                    "containsNull": True,
                                    "elementType": "string",
                                    "type": "array"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "recruitingProvinces",
                                "nullable": True,
                                "type": {
                                    "containsNull": True,
                                    "elementType": "string",
                                    "type": "array"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "recruitingSource",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "recruitingSources",
                                "nullable": True,
                                "type": {
                                    "containsNull": True,
                                    "elementType": "string",
                                    "type": "array"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "recruitingTypes",
                                "nullable": True,
                                "type": {
                                    "containsNull": True,
                                    "elementType": "string",
                                    "type": "array"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "valid_recruiting_count",
                                "nullable": True,
                                "type": "long"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "tabStats",
                "nullable": True,
                "type": {
                    "keyType": "string",
                    "type": "map",
                    "valueContainsNull": True,
                    "valueType": "long"
                }
            }
        ],
        "type": "struct"
    }
