#!/usr/bin/env python
# -*- coding: utf-8 -*-
import math
from collections import defaultdict
from datetime import timedelta

from tdr.common.utils.time_helper import convert_arbitrary_date_format
from tdr.warehouse.dwm.enterprise.dwm_enterprise_recruiting_second_dimension.utils.constant import *
from tdr.warehouse.ods.ods_certificate.meta.ods_certificate import OdsCertificate
from tdr.warehouse.ods.ods_recruiting.meta.ods_recruiting import OdsRecruiting


def merge_cert(x_data, y_data):
    return x_data


def merge_tables(x_data, y_data):
    x_data.update(y_data)
    return x_data


def mongo_recruiting_merge_reduce(x_dict, y_dict):
    """
    合并企业的招聘计算结果
    :param x_dict:
    :param y_dict:
    :return:
    """
    y_dict[DwmEnterpriseRecruitingSecondDimension.Field.recruiting_stats] += \
        x_dict[DwmEnterpriseRecruitingSecondDimension.Field.recruiting_stats]
    return y_dict


def merge_benefit(x_data, y_data):
    return x_data + y_data


def mongo_reduce_recruiting(x_recruiting_dict, y_recruiting_dict):
    """
    1. 加法合并
    2. 字符串合并
    3. 列表合并去重
    :param x_recruiting_dict:
    :param y_recruiting_dict:
    :return:
    """
    keys = x_recruiting_dict.keys()
    for k in keys:
        if k in y_recruiting_dict:
            if k in RECRUITING_KEY_SET:
                y_recruiting_dict[k] += x_recruiting_dict[k]
            elif k in [DwmEnterpriseRecruitingSecondDimension.Field.has_recruiting]:
                y_recruiting_dict[k] = x_recruiting_dict[k] or y_recruiting_dict[k]
            elif k == DwmEnterpriseRecruitingSecondDimension.Field.latest_recruiting_date:
                y_recruiting_dict[k] = x_recruiting_dict[k] if x_recruiting_dict[k] > y_recruiting_dict[k] else \
                y_recruiting_dict[k]
            else:
                y_recruiting_dict[k] = \
                    list(set(y_recruiting_dict[k] + x_recruiting_dict[k]))
        else:
            y_recruiting_dict[k] = x_recruiting_dict[k]
    return y_recruiting_dict


def filter_cert(doc):
    if doc.get(OdsCertificate.Field.certType) != '食品经营许可证':
        return False
    if doc.get(OdsCertificate.Field.certCommercialActivities) != '单位食堂':
        return False
    if OdsCertificate.Field.certEnterpriseName not in doc:
        return False
    if doc.get(OdsCertificate.Field.certStatus) != '有效':
        return False
    try:
        expire_time = convert_arbitrary_date_format(doc[OdsCertificate.Field.certExpireTime])
        assert expire_time > datetime.now()
    except Exception:
        return False
    return True


def flat_map_cert(doc):
    names = doc[OdsCertificate.Field.certEnterpriseName]
    certificate_docs = []
    id_set = set()
    for name_doc in names:
        if isinstance(name_doc.get(OdsCertificate.Field.nameId), str) and \
                len(name_doc[OdsCertificate.Field.nameId]) > 0:
            new_id = doc[OdsCertificate.Field._id] + '_' + name_doc[OdsCertificate.Field.nameId]
            if new_id not in id_set:
                id_set.add(new_id)
                name_id = name_doc[OdsCertificate.Field.nameId]
                certificate_docs.append((
                    name_id, {'has_canteen': True, '_id': name_id}
                ))
    return certificate_docs


def mongo_filter_recruiting(recruiting_dict):
    """
    1. 过滤没有招聘时间的数据

    :param recruiting_dict:
    :return:
    """
    if OdsRecruiting.Field.recruitingPublishedTime not in recruiting_dict \
            or OdsRecruiting.Field.nameId not in recruiting_dict \
            or OdsRecruiting.Field.recruitingSource not in recruiting_dict \
            or OdsRecruiting.Field.recruitingName not in recruiting_dict:
        return False
    # 招聘时间不存在
    if convert_arbitrary_date_format(
            recruiting_dict[OdsRecruiting.Field.recruitingPublishedTime]) is None:
        return False
    return True


def recruiting_count_compute(recruiting_count_dict):
    """
    招聘人数的计算
    :param recruiting_count_dict:
    :return:
    """
    if recruiting_count_dict is None:
        return False, 1.0
    try:
        if 'min' in recruiting_count_dict and 'max' in recruiting_count_dict:
            return True, (recruiting_count_dict['min'] +
                          recruiting_count_dict['max']) / 2.0
        elif 'min' in recruiting_count_dict:
            return True, recruiting_count_dict['min']
        elif 'max' in recruiting_count_dict:
            return True, recruiting_count_dict['max']
    except:
        return False, 1.0
    return False, 1.0


def working_salary_compute(working_salary_dict, recruiting_name):
    """
    薪水的计算
    :param working_salary_dict:
    :param recruiting_name:
    :return:
    """
    if working_salary_dict is None:
        return False, 0
    average_salary = 0
    try:
        if 'min' in working_salary_dict and 'max' in working_salary_dict:
            average_salary = (float(working_salary_dict['min']) +
                              float(working_salary_dict['max'])) / 2.0
        elif 'min' in working_salary_dict:
            average_salary = float(working_salary_dict['min'])
        elif 'max' in working_salary_dict:
            average_salary = float(working_salary_dict['max'])
    except:
        return False, 0
    period = working_salary_dict.get('period')
    if period == 'year':
        average_salary /= 12.0
    elif period == 'day':
        average_salary *= 30
    elif period == 'hour':
        average_salary *= 720
    elif period == 'month':
        average_salary *= 1
    else:
        # 1. 5万元（含）以上，若岗位包含“总经理、总监、主管、副总、讲师、合伙人”
        #   等这一类关键词（后续再维护），当成月薪计算；其他，当成年薪计算。
        # 2. 5万元以下，当成月薪计算；
        if average_salary < 50000.0:
            # 当成月薪
            pass
        else:
            if recruiting_name is None:
                # 当成年薪
                average_salary /= 12.0
            else:
                is_find = False
                for k in ['总经理', '总监', '主管', '副总', '讲师', '合伙人']:
                    if recruiting_name.find(k) >= 0:
                        is_find = True
                        break
                if is_find is False:
                    # 当成年薪
                    average_salary /= 12.0
                elif is_find is True and average_salary > 100000.0:
                    # 当成年薪
                    average_salary /= 12.0
    return True, average_salary


def mongo_flat_map_recruiting(recruiting_dict):
    """
    根据招聘源进行map
    :param recruiting_dict:
    :return:
    """
    data = {DwmEnterpriseRecruitingSecondDimension.Field.valid_recruiting_count: 0}
    name_id = recruiting_dict['nameId']

    recruiting_exists = recruiting_dict.get(OdsRecruiting.Field.recruitingExists, True)
    recruiting_source = recruiting_dict[OdsRecruiting.Field.recruitingSource]

    now = datetime.strptime(datetime.now().strftime('%Y-%m-%d'), '%Y-%m-%d')
    publish_time = convert_arbitrary_date_format(recruiting_dict[OdsRecruiting.Field.recruitingPublishedTime])
    count_no_default, recruiting_count = recruiting_count_compute(
        recruiting_dict.get(OdsRecruiting.Field.recruitingCount))

    date_key = publish_time.strftime('%Y-%m-01')
    if recruiting_count and date_key in RECENT_HALF_YEAR:
        data[date_key] = recruiting_count

    if (now - timedelta(days=90)) < publish_time < now:
        data.update({
            DwmEnterpriseRecruitingSecondDimension.Field.recruiting_last_three_month_no: 1,
            DwmEnterpriseRecruitingSecondDimension.Field.recruiting_last_three_month_count: recruiting_count,
        })

    if recruiting_exists is False:
        # 招聘岗位失效
        data.update({DwmEnterpriseRecruitingSecondDimension.Field.recruitingExpiredNo: 1})
    else:
        data[DwmEnterpriseRecruitingSecondDimension.Field.valid_recruiting_count] += 1
        if (now - timedelta(days=90)) < publish_time < now:
            data.update({
                DwmEnterpriseRecruitingSecondDimension.Field.recruiting_current_no: 1,
                DwmEnterpriseRecruitingSecondDimension.Field.recruiting_current_count: recruiting_count,
            })

        data[DwmEnterpriseRecruitingSecondDimension.Field.has_recruiting] = 1
        salary_no_default, working_salary = working_salary_compute(
            recruiting_dict.get(OdsRecruiting.Field.workingSalary),
            recruiting_dict.get(OdsRecruiting.Field.recruitingName)
        )
        # 代表非默认值
        if salary_no_default is True:
            real_recruiting_count = recruiting_count
            if not isinstance(recruiting_count, (float, int)) or \
                    not isinstance(working_salary, (float, int)):
                total_salary = 0
                real_recruiting_count = 0
            else:
                total_salary = recruiting_count * working_salary
        else:
            total_salary = 0
            real_recruiting_count = 0

        data.update({
            DwmEnterpriseRecruitingSecondDimension.Field.recruiting_avg_work_salary: total_salary,
            DwmEnterpriseRecruitingSecondDimension.Field.recruiting_avg_update: (now - publish_time).days,
            DwmEnterpriseRecruitingSecondDimension.Field.recruiting_sources: [recruiting_source],
            DwmEnterpriseRecruitingSecondDimension.Field.latest_recruiting_date: recruiting_dict[
                OdsRecruiting.Field.recruitingPublishedTime],
            DwmEnterpriseRecruitingSecondDimension.Field.real_recruiting_count: real_recruiting_count
        })

        if OdsRecruiting.Field.recruitingName in recruiting_dict:
            # 岗位名称, 合并列表去重
            data[DwmEnterpriseRecruitingSecondDimension.Field.recruiting_name] = \
                [recruiting_dict[OdsRecruiting.Field.recruitingName]]
            if publish_time >= (now - timedelta(days=30)):
                data[DwmEnterpriseRecruitingSecondDimension.Field.recruiting_name_last_month] = \
                    [recruiting_dict[OdsRecruiting.Field.recruitingName]]
        if OdsRecruiting.Field.recruitingType in recruiting_dict:
            # 岗位类型, 合并列表去重
            data[DwmEnterpriseRecruitingSecondDimension.Field.recruiting_types] = \
                [recruiting_dict[OdsRecruiting.Field.recruitingType]]
        if OdsRecruiting.Field.recruitingDistrict in recruiting_dict and \
                isinstance(recruiting_dict[OdsRecruiting.Field.recruitingDistrict], dict):
            # 岗位地址, 合并列表去重
            if 'province' in recruiting_dict[OdsRecruiting.Field.recruitingDistrict]:
                data[DwmEnterpriseRecruitingSecondDimension.Field.recruiting_provinces] = [
                    recruiting_dict[OdsRecruiting.Field.recruitingDistrict]['province']
                ]
            if 'city' in recruiting_dict[OdsRecruiting.Field.recruitingDistrict]:
                data[DwmEnterpriseRecruitingSecondDimension.Field.recruiting_cities] = [
                    recruiting_dict[OdsRecruiting.Field.recruitingDistrict]['city']
                ]
        if OdsRecruiting.Field.workingAddress in recruiting_dict and \
                isinstance(recruiting_dict[OdsRecruiting.Field.workingAddress], dict):
            if 'province' in recruiting_dict[OdsRecruiting.Field.workingAddress] and \
                    DwmEnterpriseRecruitingSecondDimension.Field.recruiting_provinces not in data:
                data[DwmEnterpriseRecruitingSecondDimension.Field.recruiting_provinces] = [
                    recruiting_dict[OdsRecruiting.Field.workingAddress]['province']
                ]
            if 'city' in recruiting_dict[OdsRecruiting.Field.workingAddress] and \
                    DwmEnterpriseRecruitingSecondDimension.Field.recruiting_cities not in data:
                data[DwmEnterpriseRecruitingSecondDimension.Field.recruiting_cities] = [
                    recruiting_dict[OdsRecruiting.Field.workingAddress]['city']
                ]

        # 城市列表去重
        if data.get(DwmEnterpriseRecruitingSecondDimension.Field.recruiting_cities):
            new_cities = []
            # 城市简称映射为全称
            for city_name in data[DwmEnterpriseRecruitingSecondDimension.Field.recruiting_cities]:
                if city_name in CITY_SHORT_NAME_TO_FULL_NAME_MAPPING:
                    new_city_name = CITY_SHORT_NAME_TO_FULL_NAME_MAPPING[city_name]
                    new_cities.append(new_city_name)
                else:
                    new_cities.append(city_name)
            data[DwmEnterpriseRecruitingSecondDimension.Field.recruiting_cities] = new_cities

    return [
        dict(data, mapKey=name_id + '_' + 'all'),
        dict(data, mapKey=name_id + '_' + recruiting_source)
    ]


def mongo_source_map_recruiting(data_dict):
    key = data_dict.pop('mapKey')
    return key, data_dict


def mongo_recruiting_merge_map(compute_data):
    """
    招聘计算数据分发, key为企业id
    :param compute_data: (enterprise_source, data)
    :return:
    """
    words = compute_data[0].split('_')
    result_dict = compute_data[1]
    result_dict.pop('update_time', None)

    real_person_count = result_dict.pop('real_recruiting_count', 0)
    if real_person_count <= 0.0:
        recruiting_salary = 0.0
    else:
        recruiting_salary = result_dict.get(
            DwmEnterpriseRecruitingSecondDimension.Field.recruiting_avg_work_salary, 0
        ) / real_person_count

    data = {
        DwmEnterpriseRecruitingSecondDimension.Field.recruiting_source: words[1].strip(),
        DwmEnterpriseRecruitingSecondDimension.Field.recruiting_avg_work_salary: recruiting_salary,
    }
    if DwmEnterpriseRecruitingSecondDimension.Field.recruiting_current_no in result_dict:
        data[DwmEnterpriseRecruitingSecondDimension.Field.recruiting_avg_update] = \
            int(result_dict.get(DwmEnterpriseRecruitingSecondDimension.Field.recruiting_avg_update, 0)
                / result_dict[DwmEnterpriseRecruitingSecondDimension.Field.recruiting_current_no])
    else:
        data[DwmEnterpriseRecruitingSecondDimension.Field.recruiting_avg_update] = 0

    result_dict.update(data)
    # 企业id, 企业招聘数据
    return words[0].strip(), {
        DwmEnterpriseRecruitingSecondDimension.Field.recruiting_stats: [result_dict],
        'nameId': words[0].strip()
    }


def format_merge_map(merge_data):
    """
    merge_data 分为两部分, 第一部分为企业id
    :param merge_data: (enterprise_id, data)
    :return:
    """
    _id, data = merge_data
    data.pop('update_time', None)
    data['_id'] = _id
    if data.get(OdsRecruiting.Field.recruitingExists, True):
        for each in data.get(DwmEnterpriseRecruitingSecondDimension.Field.recruiting_stats, []):
            if each[DwmEnterpriseRecruitingSecondDimension.Field.recruiting_source] == 'all':
                recruitingLastHalfYear = []
                iterator = list(each.items())
                for k, v in iterator:
                    if k in RECENT_HALF_YEAR:
                        each.pop(k)
                        recruitingLastHalfYear.append(
                            {'date': k, 'cnt': math.ceil(v)}
                        )
                if recruitingLastHalfYear:
                    data[
                        DwmEnterpriseRecruitingSecondDimension.Field.recruiting_last_half_year] = recruitingLastHalfYear
                data[DwmEnterpriseRecruitingSecondDimension.Field.tab_stats] = {
                    DwmEnterpriseRecruitingSecondDimension.TabStatsType.recruiting: each.get(
                        DwmEnterpriseRecruitingSecondDimension.Field.valid_recruiting_count, 0),
                    DwmEnterpriseRecruitingSecondDimension.TabStatsType.operating_info_recruiting: each.get(
                        DwmEnterpriseRecruitingSecondDimension.Field.valid_recruiting_count, 0),
                }
                data[DwmEnterpriseRecruitingSecondDimension.Field.recruiting_source_count] = len(
                    each.get(DwmEnterpriseRecruitingSecondDimension.Field.recruiting_sources, []))
                data[DwmEnterpriseRecruitingSecondDimension.Field.latest_recruiting_date] = each.get(
                    DwmEnterpriseRecruitingSecondDimension.Field.latest_recruiting_date)
            else:
                for key in RECENT_HALF_YEAR:
                    each.pop(key, None)

    return _id, data


def handle_benefit(data):
    name_id, benefit_list = data
    return name_id, {'_id': name_id, DwmEnterpriseRecruitingSecondDimension.Field.benefit_list: benefit_list}


def get_top_benefit(benefit_list, count=19, remove_canteen=False):
    stats = defaultdict(int)
    black_words = ['食堂', '饭堂', '餐厅']
    for i in benefit_list:
        if remove_canteen:
            flags = [_ in i for _ in black_words]
            if any(flags):
                continue
        stats[i] += 1
    items = list(stats.items())
    items.sort(key=lambda x: x[1], reverse=True)
    items = items[:count]
    return [_[0] for _ in items]


def merge_cert_benefit(data):
    name_id, doc = data
    # 企业福利合并
    has_canteen = doc.get('has_canteen', False)
    benefit_list = doc.get(DwmEnterpriseRecruitingSecondDimension.Field.benefit_list, [])
    if has_canteen:
        benefit_list = get_top_benefit(benefit_list, count=19, remove_canteen=True)
        if benefit_list:
            benefit_list = ['员工饭堂'] + benefit_list
    else:
        benefit_list = get_top_benefit(benefit_list, count=20, remove_canteen=False)
    if len(benefit_list):
        doc[DwmEnterpriseRecruitingSecondDimension.Field.benefit_list] = benefit_list
    doc.pop('has_canteen', None)
    return name_id, doc
