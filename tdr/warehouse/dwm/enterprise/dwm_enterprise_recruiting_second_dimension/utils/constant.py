#!/usr/bin/env python
# -*- coding: utf-8 -*-
from datetime import datetime

from tdr.warehouse.dwm.enterprise.dwm_enterprise_recruiting_second_dimension.meta.dwm_enterprise_recruiting_second_dimension import \
    DwmEnterpriseRecruitingSecondDimension

RECRUITING_KEY_SET = {
    DwmEnterpriseRecruitingSecondDimension.Field.recruiting_current_no,
    DwmEnterpriseRecruitingSecondDimension.Field.recruiting_current_count,
    DwmEnterpriseRecruitingSecondDimension.Field.recruiting_avg_work_salary,
    DwmEnterpriseRecruitingSecondDimension.Field.recruiting_avg_update,
    DwmEnterpriseRecruitingSecondDimension.Field.recruiting_last_three_month_no,
    DwmEnterpriseRecruitingSecondDimension.Field.recruiting_last_three_month_count,
    DwmEnterpriseRecruitingSecondDimension.Field.real_recruiting_count,
    DwmEnterpriseRecruitingSecondDimension.Field.recruitingExpiredNo,
    DwmEnterpriseRecruitingSecondDimension.Field.valid_recruiting_count,
}
NOW = datetime.now()
# 城市简称映射为城市全程（暂时为一二线城市的映射，以后有新加的可以继续添加）
CITY_SHORT_NAME_TO_FULL_NAME_MAPPING = {
    "北京": "北京市",
    "上海": "上海市",
    "广州": "广州市",
    "深圳": "深圳市",
    "成都": "成都市",
    "杭州": "杭州市",
    "重庆": "重庆市",
    "武汉": "武汉市",
    "西安": "西安市",
    "苏州": "苏州市",
    "天津": "天津市",
    "南京": "南京市",
    "长沙": "长沙市",
    "郑州": "郑州市",
    "东莞": "东莞市",
    "青岛": "青岛市",
    "沈阳": "沈阳市",
    "宁波": "宁波市",
    "昆明": "昆明市",
    "佛山": "佛山市",
    "惠州": "惠州市",
    "珠海": "珠海市",
    "中山": "中山市",
    "无锡": "无锡市",
    "合肥": "合肥市",
    "大连": "大连市",
    "福州": "福州市",
    "厦门": "厦门市",
    "济南": "济南市",
    "温州": "温州市",
    "南宁": "南宁市",
    "长春": "长春市",
    "泉州": "泉州市",
    "贵阳": "贵阳市",
    "南昌": "南昌市",
    "金华": "金华市",
    "常州": "常州市",
    "南通": "南通市",
    "嘉兴": "嘉兴市",
    "太原": "太原市",
    "徐州": "徐州市",
    "台州": "台州市",
    "烟台": "烟台市",
    "兰州": "兰州市",
    "绍兴": "绍兴市",
    "海口": "海口市",
    "扬州": "扬州市",
    "哈尔滨": "哈尔滨市",
    "石家庄": "石家庄市",
}

def subtract_n_month(dt, n):
    """月份往前推n个月"""
    assert isinstance(dt, datetime)
    month = dt.month - n
    year = dt.year
    if month <= 0:
        year -= 1
        month = 12 + month
    return datetime(year, month, 1)
RECENT_HALF_YEAR = {
    subtract_n_month(NOW, n).strftime('%Y-%m-%d') for n in list(range(1, 7))
}
RECRUITING_KEY_SET = RECRUITING_KEY_SET.union(RECENT_HALF_YEAR)