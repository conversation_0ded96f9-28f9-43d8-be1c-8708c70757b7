#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    ~~~~~~~~~~~~~~~~~~~~~~~

    Description of this file

    :author: <PERSON><PERSON><PERSON><PERSON>
    :copyright: (c) dwm_enterprise_judgment_document_compute.py, Tungee
    :date created: 2022/12/8
    :python version: 3.6

"""
import sys

from tdr.common.constant.module import ModuleNameConstant
from tdr.common.utils.spark.spark_utils import *
from tdr.warehouse.dwm.enterprise.dwm_enterprise_judgment_document.meta.dwm_enterprise_judgment_document import \
    DwmEnterpriseJudgmentDocument
from tdr.warehouse.dwm.enterprise.dwm_enterprise_judgment_document.utils.dwm_judgment_document_compute_util import *
from tdr.warehouse.utils.spark import get_rdd_from_file
JOB_NAME = 'DW_{}_{}'.format(ModuleNameConstant.enterprise, DwmEnterpriseJudgmentDocument.NAME)


def main(*args):
    """ 企业经营风险数据计算入口
    """
    [
        ods_judgment_document_input,
        dwm_judgment_document,
        n_partition
    ] = args

    spark = SparkSession.builder.appName(JOB_NAME).getOrCreate()
    sc = spark.sparkContext
    n_partition = int(n_partition)

    judgment_document_ods_rdd = get_rdd_from_file(sc, ods_judgment_document_input)

    # 裁判文书过滤去重
    jd_rdd = judgment_document_ods_rdd.filter(
        filter_judgment_document_without_name_id
    ).map(
        map_judgment_document_with_jd_case_number
    ).reduceByKey(
        reduce_judgment_document_by_jd_case_number
    ).values()
    persist_rdd(jd_rdd, dwm_judgment_document, numPartitions=n_partition)


if __name__ == '__main__':
    main(*sys.argv[1:])