# -*- coding: utf-8 -*-
"""
    dwm_enterprise_judgment_document
    ~~~~~~~
    
    Description
    
    :author: <PERSON>
    :copyright: (c) 2023, Tungee
    :date created: 2023-01-10
    :python version: 
"""
from tdr.common.constant.common import FormatType
from tdr.warehouse.ods.ods_judgment_document.meta.ods_judgment_document import OdsJudgmentDocument


class DwmEnterpriseJudgmentDocument(object):
    NAME = 'dwm_enterprise_judgment_document'

    FORMAT_TYPE = FormatType.json

    class Field:
        _id = '_id'
        create_time = 'create_time'
        import_update_time = 'import_update_time'
        jdAppealRecord = 'jdAppealRecord'
        jdCaseJudgmentDate = 'jdCaseJudgmentDate'
        jdCaseNumber = 'jdCaseNumber'
        jdCaseOfAction = 'jdCaseOfAction'
        jdCaseParties = 'jdCaseParties'
        jdCaseRelatedPersonList = 'jdCaseRelatedPersonList'
        jdCaseResult = 'jdCaseResult'
        jdCaseType = 'jdCaseType'
        jdCourt = 'jdCourt'
        jdDeleted = 'jdDeleted'
        jdFact = 'jdFact'
        jdLegalBasis = 'jdLegalBasis'
        jdPublishDate = 'jdPublishDate'
        jdReason = 'jdReason'
        jdSource = 'jdSource'
        jdTitle = 'jdTitle'
        jdTrialRound = 'jdTrialRound'
        jdType = 'jdType'
        jdUrl = 'jdUrl'
        last_update_time = 'last_update_time'
        update_time = 'update_time'

        class JdCaseOfActionField:
            fifthCause = 'fifthCause'
            firstCause = 'firstCause'
            fourthCause = 'fourthCause'
            secondCause = 'secondCause'
            thirdCause = 'thirdCause'

        class JdCaseRelatedPersonListField:
            name = 'name'
            nameId = 'nameId'
            nameIdSource = 'nameIdSource'
            type = 'type'

            class NameIdSourceField:
                name = 'name'
                nameId = 'nameId'
                source = 'source'

    INPUTS = [OdsJudgmentDocument]

    SPARK_SCHEMA = {
        "fields": [
            {
                "metadata": {

                },
                "name": "_id",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "c_id",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "create_time",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "import_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "jdAppealRecord",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "jdCaseJudgmentDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "jdCaseNumber",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "jdCaseOfAction",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "fifthCause",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "firstCause",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "fourthCause",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "secondCause",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "thirdCause",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "jdCaseParties",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "jdCaseRelatedPersonList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "identity",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "name",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameId",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameIdSource",
                                "nullable": True,
                                "type": {
                                    "fields": [
                                        {
                                            "metadata": {

                                            },
                                            "name": "name",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "nameId",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "source",
                                            "nullable": True,
                                            "type": "string"
                                        }
                                    ],
                                    "type": "struct"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "type",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "jdCaseResult",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "jdCaseType",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "jdContent",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "jdCourt",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "jdDeleted",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "jdFact",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "jdLegalBasis",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "jdLitigationResult",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "amount",
                                "nullable": True,
                                "type": {
                                    "fields": [
                                        {
                                            "metadata": {

                                            },
                                            "name": "source",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "value",
                                            "nullable": True,
                                            "type": "double"
                                        }
                                    ],
                                    "type": "struct"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "litigationStatus",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "name",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameId",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "jdPageImage",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "jdPublishDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "jdPublishYear",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "jdReason",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "jdSource",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "jdTitle",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "jdTrialRound",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "jdType",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "jdUrl",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "last_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "update_time",
                "nullable": True,
                "type": "timestamp"
            }
        ],
        "type": "struct"
    }
