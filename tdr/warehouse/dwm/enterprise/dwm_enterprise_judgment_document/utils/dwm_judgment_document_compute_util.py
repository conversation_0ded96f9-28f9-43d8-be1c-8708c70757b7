#!/usr/bin/env python
# -*- coding: utf-8 -*-
import re

from tdr.warehouse.dwm.enterprise.dwm_enterprise_judgment_document.utils.constant import *
from tdr.warehouse.dwm.enterprise.dwm_enterprise_risk.utils.constant import JD_TYPE_MAPPING
from tdr.warehouse.ods.ods_judgment_document.meta.ods_judgment_document import OdsJudgmentDocument

HOLDER_NAME_MAPPING = {}
DATE_STRING_MATCH = re.compile(r'^\d{4}-\d{2}-\d{2}$')


def filter_judgment_document_without_name_id(doc):
    """ 过滤缺少primary_key、映射、案由、数据源的数据
    """
    for main_key in [
        OdsJudgmentDocument.Field.jdSource, OdsJudgmentDocument.Field.jdCaseNumber, OdsJudgmentDocument.Field.jdCaseRelatedPersonList
    ]:
        if not doc.get(main_key):
            return False

    has_name_id = False
    for each in doc[OdsJudgmentDocument.Field.jdCaseRelatedPersonList]:
        if doc.get(OdsJudgmentDocument.Field.jdSource) in ['wenshu.court', 'ylz'] and not each.get('type'):
            each['type'] = '当事人'
        if each.get('type') not in ROLE_TYPE_LIST:
            continue
        if each.get('nameId'):
            has_name_id = True
    if not has_name_id:
        return False
    if doc[OdsJudgmentDocument.Field.jdSource] not in JD_SOURCE_PRIORITY_LIST:
        return False
    return True


def map_judgment_document_with_jd_case_number(doc):
    new_person_list, duplicate_set = [], set()
    for each in doc[OdsJudgmentDocument.Field.jdCaseRelatedPersonList]:
        _type = each.get('type')
        name_id = each.get('nameId')
        name = each.get('name')
        if _type not in ROLE_TYPE_LIST:
            continue
        if (_type, name_id, name) in duplicate_set:
            continue
        duplicate_set.add((_type, name_id, name))
        each['identity'] = JD_TYPE_MAPPING.get(each.get('type'), '其他')
        new_person_list.append(each)
    doc[OdsJudgmentDocument.Field.jdCaseRelatedPersonList] = new_person_list
    if doc.get(OdsJudgmentDocument.Field.jdPublishDate):
        doc['jdPublishYear'] = doc[OdsJudgmentDocument.Field.jdPublishDate][:4]
    jd_title = doc.get(OdsJudgmentDocument.Field.jdTitle, '')
    # 把“、”，“与”，去掉后，再作为主键判断；并且兼容，中文括号、英文括号。【即：我们认为，2个文本，如果只有“、”，“与”，“中英文括号”差别的话，这2条数据是一样的】
    jd_title = re.sub(r'[，、,与]', '', jd_title).replace('（', '(').replace('）', ')')
    return (doc[OdsJudgmentDocument.Field.jdCaseNumber], jd_title), doc


def judge_case_of_action_and_add_cause(result_doc, low_doc):
    result_jd_case_of_action = result_doc.get(OdsJudgmentDocument.Field.jdCaseOfAction)
    low_jd_case_of_action = low_doc.get(OdsJudgmentDocument.Field.jdCaseOfAction)
    if result_jd_case_of_action and low_jd_case_of_action:
        low_score = 0
        result_score = 0
        cause_list = {'firstCause': 1, 'secondCause': 2, 'thirdCause': 4, 'fourthCause': 0, 'fifthCause': 0}
        for key, val in cause_list.items():
            if result_jd_case_of_action.get(key):
                result_score = result_score + cause_list.get(key)
            if low_jd_case_of_action.get(key):
                low_score = low_score + cause_list.get(key)
        if result_score < low_score:
            result_doc[OdsJudgmentDocument.Field.jdCaseOfAction] = low_jd_case_of_action
    elif result_jd_case_of_action is None and low_jd_case_of_action:
        result_doc[OdsJudgmentDocument.Field.jdCaseOfAction] = low_jd_case_of_action
    return result_doc


def reduce_judgment_document_by_jd_case_number(x_data, y_data):
    x_source, y_source = x_data[OdsJudgmentDocument.Field.jdSource], y_data[OdsJudgmentDocument.Field.jdSource]
    if JD_SOURCE_PRIORITY_LIST.index(y_source) < JD_SOURCE_PRIORITY_LIST.index(x_source):
        y_data = judge_case_of_action_and_add_cause(y_data, x_data)
        return y_data
    x_data = judge_case_of_action_and_add_cause(x_data, y_data)
    return x_data