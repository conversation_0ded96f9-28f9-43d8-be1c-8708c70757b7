#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    ~~~~~~~~~~~~~~~~~~~~~~~

    Description of this file

    :author: l<PERSON><PERSON><PERSON>
    :copyright: (c) dwm_enterprise_risk_compute.py, Tungee
    :date created: 2022/12/8
    :python version: 3.6

"""
import sys

from tdr.common.constant.module import ModuleNameConstant
from tdr.common.utils.spark.spark_utils import *
from tdr.warehouse.dwm.enterprise.dwm_enterprise_risk.utils.dwm_enterprise_risk_util import *
from tdr.warehouse.utils.spark import get_rdd_from_file

JOB_NAME = 'DW_{}_{}'.format(ModuleNameConstant.enterprise, DwmEnterpriseRisk.NAME)


def main(*args):
    """ 企业经营风险数据计算入口
    """
    [
        ods_enterprise_anomaly_input,
        dwd_enterprise_input,
        dwm_judgment_document_input,
        dwm_court_announcement_input,
        ods_combined_punishment_input,
        ods_enforcement_input,
        ods_bankruptcy_reorganization_input,
        ods_punish_dishonesty_input,
        dwm_enterprise_risk,
        n_partition
    ] = args

    spark = SparkSession.builder.appName(JOB_NAME).getOrCreate()
    sc = spark.sparkContext
    n_partition = int(n_partition)

    dwd_enterprise_df: DataFrame = spark.read.parquet(dwd_enterprise_input)
    dwd_enterprise_rdd: RDD = preprocess_enterprise(dwd_enterprise_df)
    enterprise_anomaly_ods_rdd = get_rdd_from_file(sc, ods_enterprise_anomaly_input)
    judgment_document_dwm_rdd = get_rdd_from_file(sc, dwm_judgment_document_input)
    court_announcement_dwm_rdd = get_rdd_from_file(sc, dwm_court_announcement_input)
    combined_punishment_ods_rdd = get_rdd_from_file(sc, ods_combined_punishment_input)
    enforcement_ods_rdd = get_rdd_from_file(sc, ods_enforcement_input)
    ods_bankruptcy_reorganization_rdd = get_rdd_from_file(sc, ods_bankruptcy_reorganization_input)
    ods_punish_dishonesty_rdd = get_rdd_from_file(sc, ods_punish_dishonesty_input)
    punish_dishonesty_rdd = ods_punish_dishonesty_rdd.filter(
        lambda doc: doc.get('nameId')  # 只保留成功映射nameId的数据
    ).map(
        lambda doc: (doc['nameId'], [doc])
    ).reduceByKey(
        lambda x, y: x + y  # 根据nameId进行聚合
    ).map(
        process_punish_dishonesty
    )

    br_rdd = ods_bankruptcy_reorganization_rdd.flatMap(
        flatten_br_with_name_id
    ).map(
        lambda doc: (doc['_id'], doc)
    )

    anomaly_rdd = enterprise_anomaly_ods_rdd.filter(
        filter_anomaly
    ).map(
        map_anomaly
    ).reduceByKey(
        reduce_anomaly
    )

    risk_rdd = dwd_enterprise_rdd.map(
        lambda doc: (doc['_id'], doc)
    ).union(
        anomaly_rdd
    ).reduceByKey(
        reduce_enterprise_anomaly
    ).map(
        lambda data: data[1]
    ).filter(
        lambda doc: '_id' in doc
    ).map(
        merge_enterprise_anomaly
    )

    # ------------------------------------------
    # 行政处罚逻辑
    # ------------------------------------------
    merge_punishment_rdd = combined_punishment_ods_rdd.filter(
        lambda doc: doc.get('punishStatus') == 0  # 状态正常
    ).map(
        field_mapping
    ).groupByKey().map(
        lambda data: (data[0], dict(cal_punishment_secondary_dimension_v2(data[1]), _id=data[0]))
    )

    enforcement_rdd = enforcement_ods_rdd.filter(
        filter_enforcement
    ).map(
        map_enforcement_count
    )

    mid_enterprise_rdd = risk_rdd.map(
        map_enterprise_risk_with_name_id
    ).filter(
        filter_enterprise_without_risk
    ).union(
        judgment_document_dwm_rdd.flatMap(flat_map_judgment_document_with_name_id)
    ).union(
        court_announcement_dwm_rdd.flatMap(flat_map_court_announcement_with_name_id)
    ).union(
        enforcement_rdd
    ).union(
        merge_punishment_rdd
    ).union(
        br_rdd
    ).union(
        punish_dishonesty_rdd
    ).reduceByKey(
        reduce_operating_risk_by_name_id
    ).map(
        map_operating_risk
    )

    persist_rdd(mid_enterprise_rdd, dwm_enterprise_risk, numPartitions=n_partition)


if __name__ == '__main__':
    main(*sys.argv[1:])
