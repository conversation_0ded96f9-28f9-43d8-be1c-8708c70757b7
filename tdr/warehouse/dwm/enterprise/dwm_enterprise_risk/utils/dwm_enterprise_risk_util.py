#!/usr/bin/env python
# -*- coding: utf-8 -*-
import re
from collections import defaultdict, Counter
from copy import deepcopy
from datetime import datetime

from pyspark.sql import DataFrame
from pyspark import RDD
import pyspark.sql.functions as fn
from tdr.common.constant.common import is_valid
from tdr.common.utils.time_helper import convert_arbitrary_date_format
from tdr.common.warehourse.dict_utils import drop_none
from tdr.warehouse.dwd.dwd_enterprise.meta.dwd_enterprise import DwdEnterprise
from tdr.warehouse.dwm.enterprise.dwm_enterprise_court_announcement.meta.dwm_enterprise_court_announcement import \
    DwmEnterpriseCourtAnnouncement
from tdr.warehouse.dwm.enterprise.dwm_enterprise_risk.meta.dwm_enterprise_risk import DwmEnterpriseRisk
from tdr.warehouse.dwm.enterprise.dwm_enterprise_risk.utils.constant import *
from tdr.warehouse.ods.ods_court_announcement.meta.ods_court_announcement import OdsCourtAnnouncement
from tdr.warehouse.ods.ods_enforcement.meta.ods_enforcement import OdsEnforcement
from tdr.warehouse.ods.ods_enterprise_anomaly.meta.ods_enterprise_anomaly import OdsEnterpriseAnomaly
from tdr.warehouse.ods.ods_judgment_document.meta.ods_judgment_document import OdsJudgmentDocument

HOLDER_NAME_MAPPING = {}
DATE_STRING_MATCH = re.compile(r'^\d{4}-\d{2}-\d{2}$')
JD_CASE_TYPE_MAPPING = {
    "民事案件": "民事案件",
    "民事件": "民事案件",
    "民事案": "民事案件",
    "民案件": "民事案件",
    "执行案件": "执行案件",
    "执行案": "执行案件",
    "执案件": "执行案件",
    "非诉保全审查案件": "保全案件",
    "非诉保全": "保全案件",
    "非诉保全审案件": "保全案件",
    "刑事案件": "刑事案件",
    "行政案件": "行政案件",
    "管辖案件": "管辖案件",
    "管辖": "管辖案件",
}
JD_TYPE_KEYWORDS_MAPPING = {
    "判决": "判决书",
    "裁定": "裁定书",
    "通知": "通知书",
    "调解": "调解书",
    "令": "令",
}
CASE_NUMBER_KEY_MAPPING = {
    "执恢": "恢复执行",
    "执保": "财产保全执行",
    "执异": "执行异议",
    "执复": "执行复议",
    "执": "首次执行",
}
PUNISH_SOURCE_MAPPING = {
    "samr": '市场监管总局',
    "creditchina": '信用中国',
    "tianyancha.gsxt": '市场监管总局',
    "tianyancha.creditchina": '信用中国',
    "tianyancha.cbirc": '信用中国',
    "bjepb": '北京市生态环境局',
    "gdeei": '广东省生态环境厅',
    "qcc": '市场监管总局',
    "enterprise": '市场监管总局',
}
PATTERN = re.compile('\\(([^)]+)\\)([\\u4e00-\\u9fa5]+)(\\d+)([\\u4e00-\\u9fa5]+)(\\d+号)')


def preprocess_enterprise(dwd_business_df: DataFrame) -> RDD:
    return dwd_business_df.where(
        fn.col('_id').isNotNull()
    ).select(
        fn.col('_id'),
        fn.col('anomalyList'),
        fn.col('punishmentList'),
        fn.col('equityPledgeList'),
        fn.col('mortgageInfoList'),
        fn.col('checkInfoList'),
        fn.col('iprPledgeList'),
        fn.col('judicialAssistanceList'),
        fn.col('illegalInfoList'),
        fn.col('creditChinaPubPunishmentList'),
    ).rdd.map(
        lambda row: drop_none(row.asDict(recursive=True))
    )


def process_punish_dishonesty(data):
    doc_id, punish_dishonest_list = data
    return doc_id, {
        DwmEnterpriseRisk.Field._id: doc_id,
        DwmEnterpriseRisk.Field.has_gov_procurement_illegal: 1,
        DwmEnterpriseRisk.Field.gov_procurement_illegal_list: punish_dishonest_list,
    }


def flat_map_judgment_document_with_name_id(doc):
    publish_time = doc.get(OdsJudgmentDocument.Field.jdPublishDate) and convert_arbitrary_date_format(
        doc[OdsJudgmentDocument.Field.jdPublishDate])
    jd_case_of_action = doc.get(OdsJudgmentDocument.Field.jdCaseOfAction, {})
    cause = jd_case_of_action.get('thirdCause')  # 裁判文书案由
    jdTitle = doc.get(OdsJudgmentDocument.Field.jdTitle)  # 裁判文书标题
    jdCaseType = doc.get(OdsJudgmentDocument.Field.jdCaseType)  # 案件类型
    jdCaseNumber = doc.get(OdsJudgmentDocument.Field.jdCaseNumber)  # 裁判文书案号
    jdType = doc.get(OdsJudgmentDocument.Field.jdType)  # 文书类型
    jdTrialRound = doc.get(OdsJudgmentDocument.Field.jdTrialRound)  # 审判程序
    jdCourt = doc.get(OdsJudgmentDocument.Field.jdCourt)  # 判决法院
    jdPublishDate = doc.get(OdsJudgmentDocument.Field.jdPublishDate)
    jdCaseJudgmentDate = doc.get(OdsJudgmentDocument.Field.jdCaseJudgmentDate)  # 判决日期

    # 裁判文书案件类型映射处理
    if jdCaseType:
        jdCaseType = JD_CASE_TYPE_MAPPING.get(jdCaseType) or '其他案件'

    result_map = {}
    for each in doc[OdsJudgmentDocument.Field.jdCaseRelatedPersonList]:
        name_id = each.get('nameId')
        identity = JD_TYPE_MAPPING.get(each.get('type'), '其他')
        if not name_id:
            continue

        if name_id not in result_map:
            result_map[name_id] = {
                '_id': name_id,
                DwmEnterpriseRisk.Field.riskTypeCount: {'法律风险': 1},
                DwmEnterpriseRisk.Field.riskTypeStatsForEt: {'法律风险': 1},
                DwmEnterpriseRisk.Field.jdRoleStats: defaultdict(int),
                DwmEnterpriseRisk.Field.jdRoleStatsNew: defaultdict(int),
                DwmEnterpriseRisk.Field.hasJudgmentDocument: 1,
                DwmEnterpriseRisk.Field.tabStats: {
                    DwmEnterpriseRisk.TabStatsType.risk_judgment_document: 1
                },
            }
            if jdCaseType:
                result_map[name_id][DwmEnterpriseRisk.Field.jdCaseTypeStats] = {jdCaseType: 1}
            if jdPublishDate:
                result_map[name_id][DwmEnterpriseRisk.Field.jdPublishDateList] = [jdPublishDate]
                result_map[name_id][DwmEnterpriseRisk.Field.latestRiskInfo] = {
                    'riskType': '裁判文书', 'riskPubTime': jdPublishDate
                }
            if cause:
                result_map[name_id][DwmEnterpriseRisk.Field.jdCaseOfActionList] = [doc['jdCaseOfAction']]
                result_map[name_id][DwmEnterpriseRisk.Field.jdCaseOfActionStats] = {cause: 1}
            if jd_case_of_action:
                jd_cause_list = list(set([jd_case_of_action[_cause] for _cause in [
                    'firstCause', 'secondCause', 'thirdCause', 'fourthCause', 'fifthCause'] if
                                          jd_case_of_action.get(_cause)]))
                if jd_cause_list:
                    result_map[name_id][DwmEnterpriseRisk.Field.jdCaseOfActionDetailList] = jd_cause_list
            if publish_time:
                result_map[name_id][DwmEnterpriseRisk.Field.annualRiskCount] = {(publish_time.year, '法律风险'): 1}
                result_map[name_id][DwmEnterpriseRisk.Field.annualRiskStatsForEt] = {(publish_time.year, '法律风险'): 1}
            if jdTitle:
                result_map[name_id][DwmEnterpriseRisk.Field.jdTitleList] = [jdTitle]
            if jdCaseType:
                result_map[name_id][DwmEnterpriseRisk.Field.jdCaseTypeList] = [jdCaseType]
            if jdCaseNumber:
                result_map[name_id][DwmEnterpriseRisk.Field.jdCaseNumberList] = [jdCaseNumber]
            if jdType:
                is_other = True
                for keyword, jd_type in JD_TYPE_KEYWORDS_MAPPING.items():
                    if re.search(keyword, jdType):
                        jdType = jd_type
                        is_other = False
                        break
                if is_other:
                    jdType = '其他文书'
                result_map[name_id][DwmEnterpriseRisk.Field.jdTypeList] = [jdType]
            if jdTrialRound:
                if jdTrialRound not in ['首次执行', '恢复执行', '财产保全执行', '执行异议', '执行复议', '特别程序']:
                    is_other = True
                    for keyword in ['一审', '二审']:
                        if re.search(keyword, jdTrialRound):
                            result_map[name_id][DwmEnterpriseRisk.Field.jdTrialRoundList] = [keyword]
                            is_other = False
                            break
                    if is_other:
                        try:
                            matched = re.match(PATTERN, jdCaseNumber).groups()[3]
                        except:
                            matched = ''
                        jd_trial_round = CASE_NUMBER_KEY_MAPPING.get(matched) or '其他程序'
                        result_map[name_id][DwmEnterpriseRisk.Field.jdTrialRoundList] = [jd_trial_round]
                else:
                    result_map[name_id][DwmEnterpriseRisk.Field.jdTrialRoundList] = [jdTrialRound]
            if jdCourt:
                result_map[name_id][DwmEnterpriseRisk.Field.jdCourtList] = [jdCourt]
            if identity:
                result_map[name_id][DwmEnterpriseRisk.Field.jdIdentities] = [identity]
            if jdCaseJudgmentDate:
                result_map[name_id]['jdCaseJudgmentYearList'] = [jdCaseJudgmentDate[:4]]
        if each['type'] in PLAINTIFF_TYPE_LIST:
            result_map[name_id][DwmEnterpriseRisk.Field.jdRoleStats]['原告'] += 1
        elif each['type'] in LITIGANT_TYPE_LIST:
            result_map[name_id][DwmEnterpriseRisk.Field.jdRoleStats]['被告'] += 1
        # 案件角色（身份）统计，原来的jdRoleStats保留，避免后端引用了该字段的地方报错
        result_map[name_id][DwmEnterpriseRisk.Field.jdRoleStatsNew][identity] += 1
    return result_map.items()


def flat_map_court_announcement_with_name_id(doc):
    publish_time = doc.get(OdsCourtAnnouncement.Field.caPublishDate) and convert_arbitrary_date_format(
        doc[OdsCourtAnnouncement.Field.caPublishDate])
    ca_address = doc.get(OdsCourtAnnouncement.Field.caAddress)
    caPublishUnit = doc.get(OdsCourtAnnouncement.Field.caPublishUnit)  # 法院名称
    caContent = doc.get(OdsCourtAnnouncement.Field.caContent, '')  # 公告内容
    caTitle = doc.get(OdsCourtAnnouncement.Field.caTitle)  # 公告标题
    caPublishDate = doc.get(OdsCourtAnnouncement.Field.caPublishDate)  # 公告日期
    calPublishDate = doc.get(DwmEnterpriseCourtAnnouncement.Field.calPublishDate)  # 开庭公告日期
    ca_type = doc.get(OdsCourtAnnouncement.Field.caType)  # 公告类型
    caRelatedCaseNumber = doc.get(OdsCourtAnnouncement.Field.caRelatedCaseNumber)  # 关联案号
    caCaseStatus = doc.get(OdsCourtAnnouncement.Field.caCaseStatus)  # 案件状态
    ca_date_time = doc.get(OdsCourtAnnouncement.Field.caDate)  # 开庭日期
    caCaseReason = doc.get(OdsCourtAnnouncement.Field.caCaseReason)  # 案由
    riskType, riskPubTime = '法院公告', caPublishDate

    ca_date_time_str = None
    if ca_date_time:
        try:
            ca_date_time_str = str(ca_date_time)
            if ca_date_time_str and len(ca_date_time_str) > 10:
                ca_date_time_str = ca_date_time_str[0:10]
            datetime.strptime(ca_date_time_str, '%Y-%m-%d')
        except:
            ca_date_time_str = None

    has_open_court_session_annc = 0
    risk_court_announcement = 1
    has_court_announcement = 1
    if ca_type == OPEN_CA_SOURCE_TYPE:
        has_open_court_session_annc = 1
        risk_court_announcement = 0
        has_court_announcement = 0
        riskType, riskPubTime = '开庭公告', ca_date_time_str

    result_map = {}
    for each in (doc.get(DwmEnterpriseCourtAnnouncement.Field.caPlaintiffList, []) + doc.get(
            DwmEnterpriseCourtAnnouncement.Field.caLitigantList, [])) + doc.get(
        DwmEnterpriseCourtAnnouncement.Field.caRelationList, []):
        name_id = each.get('nameId')
        if not name_id:
            continue
        if name_id not in result_map:
            result_map[name_id] = {
                '_id': name_id,
                DwmEnterpriseRisk.Field.riskTypeCount: {'法律风险': 1},
                DwmEnterpriseRisk.Field.riskTypeStatsForEt: {'法律风险': 1},
                DwmEnterpriseRisk.Field.hasCourtAnnouncement: has_court_announcement,
                DwmEnterpriseRisk.Field.tabStats: {
                    DwmEnterpriseRisk.TabStatsType.risk_court_announcement: risk_court_announcement,
                    DwmEnterpriseRisk.TabStatsType.risk_open_court_announcement: has_open_court_session_annc
                },
            }
            if riskPubTime:
                result_map[name_id][DwmEnterpriseRisk.Field.latestRiskInfo] = {
                    'riskType': riskType, 'riskPubTime': riskPubTime
                }
            if publish_time:
                if has_court_announcement:
                    result_map[name_id][DwmEnterpriseRisk.Field.annualRiskCount] = {(publish_time.year, '法律风险'): 1}
                result_map[name_id][DwmEnterpriseRisk.Field.annualRiskStatsForEt] = {(publish_time.year, '法律风险'): 1}
                if has_court_announcement:
                    result_map[name_id][DwmEnterpriseRisk.Field.caPublishDateList] = [caPublishDate]
                elif has_open_court_session_annc:
                    result_map[name_id][DwmEnterpriseRisk.Field.openCourtPublishDateList] = [caPublishDate]

            # 有无开庭公告
            if has_open_court_session_annc:
                ca_date = convert_arbitrary_date_format(ca_date_time)
                if ca_date:
                    result_map[name_id][DwmEnterpriseRisk.Field.annualRiskCount] = {(ca_date.year, '法律风险'): 1}
                result_map[name_id][DwmEnterpriseRisk.Field.hasOpenCourtSessionAnnc] = has_open_court_session_annc
                # 案件开庭日期列表
                if ca_date_time_str:
                    result_map[name_id][DwmEnterpriseRisk.Field.openCourtSessionDateList] = [ca_date_time_str]
                # 开庭公告法院名称 / 案件开庭地址列表
                if caPublishUnit:
                    result_map[name_id][DwmEnterpriseRisk.Field.openCourtSessionAddressList] = [caPublishUnit]
                    result_map[name_id][DwmEnterpriseRisk.Field.openCourtPublishUnitList] = [caPublishUnit]
                # 开庭公告关联案号
                if caRelatedCaseNumber:
                    result_map[name_id][DwmEnterpriseRisk.Field.openCourtRelatedCaseNumberList] = [caRelatedCaseNumber]
                # 公告内容
                if caContent:
                    result_map[name_id][DwmEnterpriseRisk.Field.openCourtContentList] = [caContent]
                # 公告标题
                if caTitle:
                    result_map[name_id][DwmEnterpriseRisk.Field.openCourtTitleList] = [caTitle]
                # 案件状态
                if caCaseStatus:
                    result_map[name_id][DwmEnterpriseRisk.Field.openCourtCaseStatusList] = [caCaseStatus]
                if caCaseReason:
                    result_map[name_id][DwmEnterpriseRisk.Field.openCourtCauses] = [caCaseReason]
                if calPublishDate:
                    result_map[name_id][DwmEnterpriseRisk.Field.calPublishDateList] = [calPublishDate]
            elif has_court_announcement:
                # 公告类型
                if ca_type:
                    result_map[name_id][DwmEnterpriseRisk.Field.caTypeList] = [ca_type]
                # 如果有关联案号
                if caRelatedCaseNumber:
                    result_map[name_id][DwmEnterpriseRisk.Field.caRelatedCaseNumberList] = [caRelatedCaseNumber]
                # 法院名称
                if caPublishUnit:
                    result_map[name_id][DwmEnterpriseRisk.Field.caPublishUnitList] = [caPublishUnit]
                # 公告内容
                if caContent:
                    result_map[name_id][DwmEnterpriseRisk.Field.caContentList] = [caContent]
                # 公告标题
                if caTitle:
                    result_map[name_id][DwmEnterpriseRisk.Field.caTitleList] = [caTitle]
                # 法院公告庭审日期列表
                if ca_date_time_str:
                    result_map[name_id][DwmEnterpriseRisk.Field.caDateList] = [ca_date_time_str]
                # 法院公告庭审地点
                if ca_address:
                    result_map[name_id][DwmEnterpriseRisk.Field.caAddressList] = [ca_address]
                # 案件状态
                if caCaseStatus:
                    result_map[name_id][DwmEnterpriseRisk.Field.caCaseStatusList] = [caCaseStatus]

    for each in doc.get(OdsCourtAnnouncement.Field.caPlaintiffList, []):
        name_id = each.get('nameId')
        if name_id in result_map and has_open_court_session_annc:
            result_map[name_id][DwmEnterpriseRisk.Field.openCourtIdentities] = ['原告']
    for each in doc.get(OdsCourtAnnouncement.Field.caLitigantList, []):
        name_id = each.get('nameId')
        if name_id in result_map and has_open_court_session_annc:
            result_map[name_id][DwmEnterpriseRisk.Field.openCourtIdentities] = ['被告']
    return result_map.items()


def reduce_operating_risk_by_name_id(x_data, y_data):
    """ 按企业合并二次维度数据
    """
    result = {'_id': x_data['_id']}
    x = x_data.get('latestRiskInfo', {})
    y = y_data.get('latestRiskInfo', {})
    if x and y:
        if x.get('riskPubTime') > y.get('riskPubTime'):
            result['latestRiskInfo'] = x
        else:
            result['latestRiskInfo'] = y
    elif x or y:
        result['latestRiskInfo'] = x or y

    # 是否有xx
    for key in [
        DwmEnterpriseRisk.Field.has_court_announcement,
        DwmEnterpriseRisk.Field.has_judgment_document,
        DwmEnterpriseRisk.Field.has_punishment,
        DwmEnterpriseRisk.Field.has_open_court_session_annc,
        DwmEnterpriseRisk.Field.has_gov_procurement_illegal,
    ]:
        if x_data.get(key, 0) or y_data.get(key, 0):
            result[key] = 1

    # 列表、高级筛选
    for key in [
        DwmEnterpriseRisk.Field.jdCaseOfActionList,
        'jdCaseJudgmentYearList'
    ]:
        if x_data.get(key) or y_data.get(key):
            result[key] = x_data.get(key, []) + y_data.get(key, [])

    for key in [
        DwmEnterpriseRisk.Field.caTypeList,
        DwmEnterpriseRisk.Field.caCaseStatusList,
        DwmEnterpriseRisk.Field.caPublishDateList,
        DwmEnterpriseRisk.Field.caPublishUnitList,
        DwmEnterpriseRisk.Field.caRelatedCaseNumberList,
        DwmEnterpriseRisk.Field.caContentList,
        DwmEnterpriseRisk.Field.caTitleList,
        DwmEnterpriseRisk.Field.caDateList,
        DwmEnterpriseRisk.Field.caAddressList,
        DwmEnterpriseRisk.Field.openCourtCaseStatusList,
        DwmEnterpriseRisk.Field.openCourtPublishUnitList,
        DwmEnterpriseRisk.Field.openCourtRelatedCaseNumberList,
        DwmEnterpriseRisk.Field.openCourtPublishDateList,
        DwmEnterpriseRisk.Field.openCourtSessionDateList,
        DwmEnterpriseRisk.Field.openCourtContentList,
        DwmEnterpriseRisk.Field.openCourtSessionAddressList,
        DwmEnterpriseRisk.Field.openCourtTitleList,
        DwmEnterpriseRisk.Field.jdPublishDateList,
        DwmEnterpriseRisk.Field.jdTitleList,
        DwmEnterpriseRisk.Field.jdCaseTypeList,
        DwmEnterpriseRisk.Field.jdCaseNumberList,
        DwmEnterpriseRisk.Field.jdTypeList,
        DwmEnterpriseRisk.Field.jdTrialRoundList,
        DwmEnterpriseRisk.Field.jdCourtList,
        DwmEnterpriseRisk.Field.jdIdentities,
        DwmEnterpriseRisk.Field.openCourtIdentities,
        DwmEnterpriseRisk.Field.openCourtCauses,
        DwmEnterpriseRisk.Field.jdCaseOfActionDetailList,
        DwmEnterpriseRisk.Field.calPublishDateList,
    ]:
        if x_data.get(key) or y_data.get(key):
            result[key] = list(set(x_data.get(key, []) + y_data.get(key, [])))

    # tab count
    tab_stats = defaultdict(int)
    for key, value in x_data.get(DwmEnterpriseRisk.Field.tabStats, {}).items():
        tab_stats[key] += value
    for key, value in y_data.get(DwmEnterpriseRisk.Field.tabStats, {}).items():
        tab_stats[key] += value
    if tab_stats:
        result[DwmEnterpriseRisk.Field.tabStats] = tab_stats

    # 展示用二次维度
    for key in [
        'riskTypeCount', 'jdCaseTypeStats', 'annualRiskCount',
        'jdCaseOfActionStats', 'jdRoleStats', 'jdRoleStatsNew',
        'riskTypeStatsForEt', 'annualRiskStatsForEt',
    ]:
        if x_data.get(key) or y_data.get(key):
            result[key] = defaultdict(int)
            for _type, count in x_data.get(key, {}).items():
                result[key][_type] += count
            for _type, count in y_data.get(key, {}).items():
                result[key][_type] += count

    # 透传
    for key in [
        DwmEnterpriseRisk.Field.punishmentCount,
        DwmEnterpriseRisk.Field.lastPunishmentDate,
        DwmEnterpriseRisk.Field.punishmentTypeList,
        DwmEnterpriseRisk.Field.mergePunishmentList,
        DwmEnterpriseRisk.Field.revenuePunishmentList,
        DwmEnterpriseRisk.Field.taxAbnormalList,
        DwmEnterpriseRisk.Field.dicisionDateList,
        DwmEnterpriseRisk.Field.punishContentList,
        DwmEnterpriseRisk.Field.lastDicisionDate,
        DwmEnterpriseRisk.Field.punishAuthorityList,
        DwmEnterpriseRisk.Field.gov_procurement_illegal_list,
    ]:
        if key in x_data:
            result[key] = x_data[key]
        elif key in y_data:
            result[key] = y_data[key]
    return result


def map_operating_risk(data):
    """ 输出二次维度
    """
    _id, doc = data
    open_court_number = doc.get(DwmEnterpriseRisk.Field.tabStats, {}).get(
        DwmEnterpriseRisk.TabStatsType.risk_open_court_announcement, 0)
    if open_court_number:
        doc[DwmEnterpriseRisk.Field.openCourtNumber] = open_court_number
    # 维度数据去重
    if doc.get(DwmEnterpriseRisk.Field.caTypeList):
        doc[DwmEnterpriseRisk.Field.caTypeList] = list(
            set(doc[DwmEnterpriseRisk.Field.caTypeList]))

    if doc.get(DwmEnterpriseRisk.Field.jdCaseOfActionList):
        action_set, third_cause_set = set(), set()
        new_action_list = []
        for each in doc[DwmEnterpriseRisk.Field.jdCaseOfActionList]:
            second_cause = each.get('secondCause')
            third_cause = each.get('thirdCause')
            if second_cause and third_cause \
                    and (second_cause, third_cause) not in action_set:
                action_set.add((second_cause, third_cause))
                third_cause_set.add(third_cause)
                new_action_list.append({
                    'secondCause': second_cause,
                    'thirdCause': third_cause,
                })
        doc[DwmEnterpriseRisk.Field.jdCaseOfActionList] = new_action_list
        if third_cause_set:
            doc[DwmEnterpriseRisk.Field.jdThirdCauseList] = list(set(third_cause_set))

    if doc.get(DwmEnterpriseRisk.Field.riskTypeCount):
        total = sum(doc[DwmEnterpriseRisk.Field.riskTypeCount].values())
        law_risk_ratio = float(doc[DwmEnterpriseRisk.Field.riskTypeCount].get('法律风险', 0)) / total
        doc[DwmEnterpriseRisk.Field.riskTypeStats] = [
            {'type': '法律风险', 'ratio': law_risk_ratio},
            {'type': '经营风险', 'ratio': 1 - law_risk_ratio}
        ]
    else:
        doc.pop(DwmEnterpriseRisk.Field.riskTypeStats, None)

    # 工业大脑风险统计
    if doc.get(DwmEnterpriseRisk.Field.riskTypeStatsForEt):
        total = sum(doc[DwmEnterpriseRisk.Field.riskTypeStatsForEt].values())
        law_risk_ratio = float(doc[DwmEnterpriseRisk.Field.riskTypeStatsForEt].get('法律风险', 0)) / total
        doc[DwmEnterpriseRisk.Field.riskTypeStatsForEt] = [
            {'type': '法律风险', 'ratio': law_risk_ratio},
            {'type': '经营风险', 'ratio': 1 - law_risk_ratio}
        ]

    if doc.get(DwmEnterpriseRisk.Field.jdRoleStats):
        total = sum(doc[DwmEnterpriseRisk.Field.jdRoleStats].values())
        plaintiff_ratio = float(doc[DwmEnterpriseRisk.Field.jdRoleStats].get('原告', 0)) / total
        doc[DwmEnterpriseRisk.Field.jdRoleStats] = [
            {'type': '原告', 'ratio': plaintiff_ratio},
            {'type': '被告', 'ratio': 1 - plaintiff_ratio}
        ]

    annualRiskCount = doc.get(DwmEnterpriseRisk.Field.annualRiskCount)
    if annualRiskCount:
        new_result = []
        for (year, _type), count in annualRiskCount.items():
            new_result.append({'year': year, 'type': _type, 'count': count})
        doc[DwmEnterpriseRisk.Field.annualRiskStats] = new_result
        doc[DwmEnterpriseRisk.Field.annualRiskCount] = {f'{year}_{_type}': count for (year, _type), count in
                                                        annualRiskCount.items()}
    else:
        doc.pop(DwmEnterpriseRisk.Field.annualRiskStats, None)

    # 工业大脑风险年度统计
    if doc.get(DwmEnterpriseRisk.Field.annualRiskStatsForEt):
        new_result = []
        for (year, _type), count in doc[DwmEnterpriseRisk.Field.annualRiskStatsForEt].items():
            new_result.append({'year': year, 'type': _type, 'count': count})
        doc[DwmEnterpriseRisk.Field.annualRiskStatsForEt] = new_result

    for key in [DwmEnterpriseRisk.Field.jdCaseTypeStats, DwmEnterpriseRisk.Field.jdCaseOfActionStats]:
        if key not in doc:
            continue
        new_result = []
        for _type, count in doc[key].items():
            new_result.append({'type': _type, 'count': count})
        doc[key] = new_result
    jd_number = doc.get(DwmEnterpriseRisk.Field.tabStats, {}).get(
        DwmEnterpriseRisk.TabStatsType.risk_judgment_document, 0
    )
    if jd_number:
        doc[DwmEnterpriseRisk.Field.jd_number] = jd_number
    jdPublishDateList = doc.get(DwmEnterpriseRisk.Field.jdPublishDateList, [])
    doc[DwmEnterpriseRisk.Field.jdPublishYearList] = list(set([each[:4] for each in jdPublishDateList if each]))
    # 年度涉案统计
    jdCaseJudgmentYearList = doc.pop('jdCaseJudgmentYearList', [])
    if jdCaseJudgmentYearList:
        doc[DwmEnterpriseRisk.Field.jdAnnualCaseStats] = dict(Counter(jdCaseJudgmentYearList))
    return doc


def filter_enterprise_without_risk(data):
    _, stats_result = data
    return True if stats_result else False


def map_enterprise_risk_with_name_id(doc):
    result = {
        '_id': doc['_id'],
        DwmEnterpriseRisk.Field.riskTypeCount: defaultdict(int),
        DwmEnterpriseRisk.Field.annualRiskCount: defaultdict(int),
        DwmEnterpriseRisk.Field.riskTypeStatsForEt: defaultdict(int),
        DwmEnterpriseRisk.Field.annualRiskStatsForEt: defaultdict(int),
    }
    for key, time_field in [
        (DwdEnterprise.Field.anomalyList, 'createDate'),  # 经营异常列表
        (DwdEnterprise.Field.equityPledgeList, 'publicationDate'),  # 股权出质登记信息
        (DwdEnterprise.Field.mortgageInfoList, 'date'),  # 动产抵押信息
        (DwdEnterprise.Field.checkInfoList, 'date'),  # 抽查检查信息
        (DwdEnterprise.Field.iprPledgeList, 'iprPledgePublicDate'),  # 知识产权出质信息列表
        (DwdEnterprise.Field.illegalInfoList, 'createDate'),  # 严重违法信息
    ]:
        for item in doc.get(key, []):
            result[DwmEnterpriseRisk.Field.riskTypeCount]['经营风险'] += 1
            result[DwmEnterpriseRisk.Field.riskTypeStatsForEt]['经营风险'] += 1
            if item.get(time_field):
                dt = convert_arbitrary_date_format(item[time_field])
                if dt:
                    result[DwmEnterpriseRisk.Field.annualRiskCount][(dt.year, '经营风险')] += 1
                    result[DwmEnterpriseRisk.Field.annualRiskStatsForEt][(dt.year, '经营风险')] += 1

    for item in doc.get(DwdEnterprise.Field.judicialAssistanceList, []):  # 司法协助信息
        result[DwmEnterpriseRisk.Field.riskTypeCount]['法律风险'] += 1
        result[DwmEnterpriseRisk.Field.riskTypeStatsForEt]['经营风险'] += 1
        dt = item.get('freezeInfo', {}).get('publicationDate') \
             or item.get('freezeKeepInfo', {}).get('publicationDate') \
             or item.get('equityChangeInfo', {}).get('executionDate')

        dt = dt and convert_arbitrary_date_format(dt)
        if dt:
            result[DwmEnterpriseRisk.Field.annualRiskCount][(dt.year, '法律风险')] += 1
            result[DwmEnterpriseRisk.Field.annualRiskStatsForEt][(dt.year, '经营风险')] += 1
    return doc['_id'], result


def map_enforcement_count(doc):
    count = 0
    year_stat = defaultdict(int)
    if OdsEnforcement.Field.efCaseNumberDetail in doc:
        try:
            dt = convert_arbitrary_date_format(doc[OdsEnforcement.Field.efCaseCreateTime])
            assert dt is not None
        except:
            pass
        else:
            count += 1
            year_stat[dt.year] += 1
    if OdsEnforcement.Field.efDishonestCaseNumber in doc:
        try:
            dt = convert_arbitrary_date_format(doc[OdsEnforcement.Field.efDishonestCaseCreateTime])
            assert dt is not None
        except:
            pass
        else:
            count += 1
            year_stat[dt.year] += 1
    if OdsEnforcement.Field.efLimitedPersonCaseNumber in doc:
        try:
            dt = convert_arbitrary_date_format(doc[OdsEnforcement.Field.efLimitedPersonCaseCreateTime])
            assert dt is not None
        except:
            pass
        else:
            count += 1
            year_stat[dt.year] += 1
    if OdsEnforcement.Field.efTermCaseNumber in doc:
        try:
            dt = convert_arbitrary_date_format(doc[OdsEnforcement.Field.efTermCaseCreateTime])
            assert dt is not None
        except:
            pass
        else:
            count += 1
            year_stat[dt.year] += 1
    result = {
        '_id': doc['nameId'],
        DwmEnterpriseRisk.Field.riskTypeStatsForEt: dict(),
        DwmEnterpriseRisk.Field.annualRiskCount: dict(),
        DwmEnterpriseRisk.Field.annualRiskStatsForEt: dict()
    }
    if count > 0:
        result[DwmEnterpriseRisk.Field.riskTypeCount] = {'法律风险': count}
    for k, v in year_stat.items():
        if v:
            result[DwmEnterpriseRisk.Field.annualRiskCount][(k, '法律风险')] = v
    return result['_id'], result


def filter_enforcement(doc):
    if 'nameId' not in doc:
        return False
    fields = [OdsEnforcement.Field.efCaseNumber, OdsEnforcement.Field.efCaseNumberDetail,
              OdsEnforcement.Field.efDishonestCaseNumber]
    flags = [i in doc for i in fields]
    if not any(flags):
        return False
    if doc.get(OdsEnforcement.Field.efAvailableOnWebsite) is False:
        return False
    return True


def format_date(value):
    return convert_arbitrary_date_format(value, TIME_FORMAT)


def cal_punishment_secondary_dimension(punishment_list):
    punishment_list = list(punishment_list)  # 迭代器
    # 计算【税务局行政处罚列表】，并从【行政处罚列表】中将【税务局行政处罚列表】抽出来
    # 税务局行政处罚定义: 是指目前已有的行政处罚中，决定机关 或 行政处罚内容 中包含“税”字即认为此行政处罚为税务局行政处罚。
    keyword = "税"
    is_match = False
    tax_abnormal_list = []
    revenue_punishment_list = []
    for row in punishment_list[::-1]:
        if keyword in row.get("content", "") or keyword in row.get("authority", ""):
            is_match = True
            revenue_punishment_list.append(row)
            punishment_list.remove(row)

    if is_match:
        tax_abnormal_list.append("税务局行政处罚")

    result = {
        "riskTypeCount": defaultdict(int),
        "riskTypeStatsForEt": defaultdict(int),
        "annualRiskCount": defaultdict(int),
        "annualRiskStatsForEt": defaultdict(int),
        'hasPunishment': 0,
        'punishmentCount': 0,
        'punishmentList': punishment_list,
        'tabStats': {
            'risk::punishment': len(punishment_list),
        },
    }
    type_set, last_date, authority_set = set(), None, set()
    dicision_date_set, punish_content_set, last_dicision_date = set(), set(), None
    for each in punishment_list:
        result['punishmentCount'] += 1
        result["riskTypeCount"]["经营风险"] += 1
        result["riskTypeStatsForEt"]["经营风险"] += 1
        if is_valid(each, 'type', str):
            type_set.add(each['type'])
        if is_valid(each, 'publishDate', str):
            _date = format_date(each['publishDate'])
            if _date and (not last_date or last_date < _date):
                last_date = _date
        if is_valid(each, "decisionDate", str):
            dt = convert_arbitrary_date_format(each["decisionDate"])
            if last_dicision_date is None or last_dicision_date < each["decisionDate"]:
                last_dicision_date = each["decisionDate"]
            if dt:
                dicision_date_set.add(each["decisionDate"])
                result["annualRiskCount"][(dt.year, "经营风险")] += 1
                result["annualRiskStatsForEt"][(dt.year, "经营风险")] += 1
        if is_valid(each, 'content', str):
            punish_content_set.add(each['content'])
        if is_valid(each, 'authority', str):
            authority_set.add(each['authority'])
    if result['punishmentCount']:
        result['hasPunishment'] = 1
    if last_date:
        result['lastPunishmentDate'] = last_date.strftime("%Y-%m-%d")
        result['latestRiskInfo'] = {
            'riskType': '行政处罚',
            'riskPubTime': last_date.strftime("%Y-%m-%d")
        }
    # 行政处罚类型
    if type_set:
        result['punishmentTypeList'] = list(type_set)
    # 行政处罚决定日期
    if dicision_date_set:
        result['dicisionDateList'] = list(dicision_date_set)
    # 行政处罚内容
    if punish_content_set:
        result['punishContentList'] = list(punish_content_set)
    if authority_set:
        result['punishAuthorityList'] = list(authority_set)
    # 最新行政处罚决定日期
    if last_dicision_date:
        result['lastDicisionDate'] = last_dicision_date
    if tax_abnormal_list:
        result['taxAbnormalList'] = tax_abnormal_list
    if revenue_punishment_list:
        result['revenuePunishmentList'] = revenue_punishment_list
        revenue_punishment_list.sort(key=lambda x: x.get('publishDate') or '', reverse=True)
        publish_date = revenue_punishment_list[0].get('publishDate')
        if publish_date and (not last_date or publish_date > last_date.strftime("%Y-%m-%d")):
            result['latestRiskInfo'] = {
                'riskType': '税务局行政处罚',
                'riskPubTime': publish_date
            }

    return result


def field_mapping(doc):
    new_doc = {'_id': doc['_id']}
    for k, v in PUNISHMENT_KEY_MAPPING.items():
        if doc.get(k):
            new_doc[v] = doc[k]
    sources = [PUNISH_SOURCE_MAPPING.get(source) for source in new_doc.get('sources', [])
               if PUNISH_SOURCE_MAPPING.get(source)]
    if sources:
        new_doc['sources'] = sources
    return doc['nameId'], new_doc


def cal_punishment_secondary_dimension_v2(punishment_list):
    punishment_list = list(punishment_list)  # 迭代器
    # 计算【税务局行政处罚列表】，并从【行政处罚列表】中将【税务局行政处罚列表】抽出来
    # 税务局行政处罚定义: 是指目前已有的行政处罚中，决定机关 或 行政处罚内容 中包含“税”字即认为此行政处罚为税务局行政处罚。
    keyword = "税"
    is_match = False
    tax_abnormal_list = []
    revenue_punishment_list = []
    for row in punishment_list[::-1]:
        if keyword in row.get("content", "") or keyword in row.get("authority", ""):
            is_match = True
            revenue_punishment_list.append(row)
            punishment_list.remove(row)

    if is_match:
        tax_abnormal_list.append("税务局行政处罚")

    result = {
        "riskTypeCount": defaultdict(int),
        "riskTypeStatsForEt": defaultdict(int),
        "annualRiskCount": defaultdict(int),
        "annualRiskStatsForEt": defaultdict(int),
        'hasPunishment': 0,
        'punishmentCount': 0,
        'punishmentList': punishment_list,
        'tabStats': {
            'risk::punishment': len(punishment_list),
        },
    }
    type_set, last_date, authority_set = set(), None, set()
    dicision_date_set, punish_content_set, last_dicision_date = set(), set(), None
    for each in punishment_list:
        result['punishmentCount'] += 1
        result["riskTypeCount"]["经营风险"] += 1
        result["riskTypeStatsForEt"]["经营风险"] += 1
        if is_valid(each, 'type', str):
            type_set.add(each['type'])
        if is_valid(each, 'publishDate', str):
            _date = format_date(each['publishDate'])
            if _date and (not last_date or last_date < _date):
                last_date = _date
        if is_valid(each, "decisionDate", str):
            dt = convert_arbitrary_date_format(each["decisionDate"])
            if last_dicision_date is None or last_dicision_date < each["decisionDate"]:
                last_dicision_date = each["decisionDate"]
            if dt:
                dicision_date_set.add(each["decisionDate"])
                result["annualRiskCount"][(dt.year, "经营风险")] += 1
                result["annualRiskStatsForEt"][(dt.year, "经营风险")] += 1
        if is_valid(each, 'content', str):
            punish_content_set.add(each['content'])
        if is_valid(each, 'authority', str):
            authority_set.add(each['authority'])
    if result['punishmentCount']:
        result['hasPunishment'] = 1
    if last_date:
        result['lastPunishmentDate'] = last_date.strftime("%Y-%m-%d")
        result['latestRiskInfo'] = {
            'riskType': '行政处罚',
            'riskPubTime': last_date.strftime("%Y-%m-%d")
        }
    # 行政处罚类型
    if type_set:
        result['punishmentTypeList'] = list(type_set)
    # 行政处罚决定日期
    if dicision_date_set:
        result['dicisionDateList'] = list(dicision_date_set)
    # 行政处罚内容
    if punish_content_set:
        result['punishContentList'] = list(punish_content_set)
    if authority_set:
        result['punishAuthorityList'] = list(authority_set)
    # 最新行政处罚决定日期
    if last_dicision_date:
        result['lastDicisionDate'] = last_dicision_date
    if tax_abnormal_list:
        result['taxAbnormalList'] = tax_abnormal_list
    if revenue_punishment_list:
        result['revenuePunishmentList'] = revenue_punishment_list
        revenue_punishment_list.sort(key=lambda x: x.get('publishDate') or '', reverse=True)
        publish_date = revenue_punishment_list[0].get('publishDate')
        if publish_date and (not last_date or publish_date > last_date.strftime("%Y-%m-%d")):
            result['latestRiskInfo'] = {
                'riskType': '税务局行政处罚',
                'riskPubTime': publish_date
            }

    return result


def merge_punishment_list_and_credit_china_pub_punishment_list(doc):
    """
    合并行政处罚列表（工商信息的行政处罚列表，信用中国的行政处罚列表）
    :param doc:
    :return:
    """
    new_punishment_list = []
    punishment_list = doc.get("punishmentList", [])
    for punishment in punishment_list:
        punishment['source'] = 'samr'
        if punishment.get('is_deleted') != 1:
            new_punishment_list.append(punishment)
    credit_china_pub_punishment_list = doc.pop("creditChinaPubPunishmentList", [])
    for row in credit_china_pub_punishment_list:
        row['source'] = 'creditchina'
        if row.get("reason"):
            row["content"] = row["reason"]
            row.pop("reason")
        if row.get("is_deleted") != 1:
            new_punishment_list.append(row)
    doc["punishmentList"] = new_punishment_list
    return doc


def select_punishment(punishment_list):
    punishment_list = list(punishment_list)
    punishment_list.sort(
        key=lambda x: ORDERED_PUNISHMENT_SOURCE_LIST.index(x["source"])
    )
    doc = punishment_list[0]
    # 计算所有有效来源
    doc['sources'] = list(set([
        PUNISH_SOURCE_MAPPING[each['source']] for each in punishment_list if each.get("source")]))
    return doc


def extract_punish_uuid(punish_id):
    """ 提取行政处罚唯一标识
    * 去除不可见字符以及常见括号
    """
    return re.sub(r'[\s\(\)《》\[\]〔〕【】（）﹝﹞{}<>]', '', punish_id)


def filter_anomaly(doc):
    if not isinstance(doc, dict):
        return False
    if 'nameId' not in doc:
        return False
    return True


def map_anomaly(doc):
    return doc['nameId'], {'enterprise_anomaly_list': [doc]}


def reduce_anomaly(x_data, y_data):
    return {'enterprise_anomaly_list': x_data['enterprise_anomaly_list'] + y_data['enterprise_anomaly_list']}


def reduce_enterprise_anomaly(x_data, y_data):
    x_data.update(y_data)
    return x_data


def merge_enterprise_anomaly(doc):
    enterprise_anomaly_list = doc.get('enterprise_anomaly_list', [])
    # 格式化经营异常公告并排序
    anomaly_list = []
    for anomaly in enterprise_anomaly_list:
        create_date = anomaly.get(OdsEnterpriseAnomaly.Field.anomalyCreateDate)
        if create_date:
            create_date = convert_arbitrary_date_format(create_date)
        remove_date = anomaly.get(OdsEnterpriseAnomaly.Field.anomalyRemoveDate)
        if remove_date:
            remove_date = convert_arbitrary_date_format(remove_date)
        if create_date:
            new_anomaly = {
                "type": "create",
                "source": "enterprise_anomaly",
                "sort_key": create_date,
            }
        elif remove_date:
            new_anomaly = {
                "type": "remove",
                "source": "enterprise_anomaly",
                "sort_key": remove_date,
            }
        else:
            continue

        for key, map_key in [
            (OdsEnterpriseAnomaly.Field.anomalyCreateDate,
             DwmEnterpriseRisk.AnomalyList.create_date),
            (OdsEnterpriseAnomaly.Field.anomalyCreateReason,
             DwmEnterpriseRisk.AnomalyList.create_reason),
            (OdsEnterpriseAnomaly.Field.anomalyCreateAuthority,
             DwmEnterpriseRisk.AnomalyList.create_authority),
            (OdsEnterpriseAnomaly.Field.anomalyRemoveDate,
             DwmEnterpriseRisk.AnomalyList.remove_date),
            (OdsEnterpriseAnomaly.Field.anomalyRemoveReason,
             DwmEnterpriseRisk.AnomalyList.remove_reason),
            (OdsEnterpriseAnomaly.Field.anomalyRemoveAuthority,
             DwmEnterpriseRisk.AnomalyList.remove_authority),
        ]:
            if key in anomaly:
                new_anomaly[map_key] = anomaly[key]
        anomaly_list.append(new_anomaly)
    anomaly_list.sort(key=lambda dic: dic["sort_key"], reverse=True)

    uuid_list = set()
    gs_anomaly_list = []
    if DwmEnterpriseRisk.Field.anomalyList in doc and isinstance(doc[DwmEnterpriseRisk.Field.anomalyList], list):
        for anomaly in doc[DwmEnterpriseRisk.Field.anomalyList]:
            if anomaly.get('is_deleted') and anomaly.get('is_deleted') != '0':
                continue
            create_date = anomaly.get(DwmEnterpriseRisk.Field.create_date)
            if create_date:
                create_date = convert_arbitrary_date_format(create_date)
            remove_date = anomaly.get(DwmEnterpriseRisk.Field.remove_date)
            if remove_date:
                remove_date = convert_arbitrary_date_format(remove_date)
            if not create_date and not remove_date:
                continue
            anomaly["source"] = "enterprise"
            if remove_date:
                uuid_list.add(remove_date)
                anomaly["sort_key"] = remove_date
            if create_date:
                uuid_list.add(create_date)
                anomaly["sort_key"] = create_date
            gs_anomaly_list.append(anomaly)

    # 显示用
    new_anomaly_list = deepcopy(gs_anomaly_list)
    for anomaly in anomaly_list:
        if anomaly["sort_key"] not in uuid_list:
            uuid_list.add(anomaly["sort_key"])
            new_anomaly_list.append(anomaly)
    new_anomaly_list.sort(key=lambda dic: dic["sort_key"])
    doc[DwmEnterpriseRisk.Field.anomalyList] = new_anomaly_list
    return doc


def flatten_br_with_name_id(doc):
    """根据申请人和被申请人展开"""
    br_applicant_list = doc.pop('brApplicantList', [])
    br_respondent_list = doc.pop('brRespondentList', [])
    br_publish_date = doc.get('brPublishDate', '1970-01-01')
    pub_date = convert_arbitrary_date_format(br_publish_date)

    results = []

    for each in br_applicant_list:
        name_id = each.get('nameId')
        if not name_id:
            continue
        new_doc = {
            '_id': name_id,
            DwmEnterpriseRisk.Field.riskTypeCount: {'法律风险': 1},
        }
        if pub_date:
            new_doc[DwmEnterpriseRisk.Field.annualRiskCount] = {(pub_date.year, '法律风险'): 1}
        results.append(new_doc)

    for each in br_respondent_list:
        name_id = each.get('nameId')
        if not name_id:
            continue
        new_doc = {
            '_id': name_id,
            DwmEnterpriseRisk.Field.riskTypeCount: {'法律风险': 1},
        }
        if pub_date:
            new_doc[DwmEnterpriseRisk.Field.annualRiskCount] = {(pub_date.year, '法律风险'): 1}
        results.append(new_doc)

    return results
