#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    ~~~~~~~~~~~~~~~~~~~~~~~

    Description of this file

    :author: l<PERSON><PERSON><PERSON>
    :copyright: (c) dwm_enterprise_risk.py, Tungee
    :date created: 2022/12/5
    :python version: 3.6

"""
from tdr.common.constant.common import FormatType
from tdr.warehouse.dwd.dwd_enterprise.meta.dwd_enterprise import DwdEnterprise
from tdr.warehouse.dwm.enterprise.dwm_enterprise_court_announcement.meta.dwm_enterprise_court_announcement import \
    DwmEnterpriseCourtAnnouncement
from tdr.warehouse.dwm.enterprise.dwm_enterprise_judgment_document.meta.dwm_enterprise_judgment_document import \
    DwmEnterpriseJudgmentDocument
from tdr.warehouse.ods.ods_bankruptcy_reorganization.meta.ods_bankruptcy_reorganization import \
    OdsBankruptcyReorganization
from tdr.warehouse.ods.ods_enforcement.meta.ods_enforcement import OdsEnforcement
from tdr.warehouse.ods.ods_enterprise_anomaly.meta.ods_enterprise_anomaly import OdsEnterpriseAnomaly
from tdr.warehouse.ods.ods_combined_punishment.meta.ods_combined_punishment import OdsCombinedPunishment
from tdr.warehouse.ods.ods_punish_dishonesty.meta.ods_punish_dishonesty import OdsPunishDishonesty


class DwmEnterpriseRisk(object):
    NAME = 'dwm_enterprise_risk'

    FORMAT_TYPE = FormatType.json

    # 字段名
    class Field:
        _id = '_id'
        anomalyList = 'anomalyList'
        punishmentList = 'punishmentList'
        riskTypeStatsForEt = 'riskTypeStatsForEt'
        annualRiskCount = 'annualRiskCount'
        annualRiskStats = 'annualRiskStats'  # 该字段暂时保留，统一版已改为annualRiskStatsNew
        annualRiskStatsForEt = 'annualRiskStatsForEt'
        riskTypeCount = 'riskTypeCount'
        riskTypeStats = 'riskTypeStats'  # 该字段暂时保留，统一版已改为riskTypeStatsNew
        jdRoleStats = 'jdRoleStats'
        jdRoleStatsNew = 'jdRoleStatsNew'
        hasJudgmentDocument = 'hasJudgmentDocument'
        tabStats = 'tabStats'
        jdCaseTypeStats = 'jdCaseTypeStats'
        jdPublishDateList = 'jdPublishDateList'
        jdPublishYearList = 'jdPublishYearList'
        latestRiskInfo = 'latestRiskInfo'
        jdThirdCauseList = 'jdThirdCauseList'
        jdCaseOfActionList = 'jdCaseOfActionList'
        jdCaseOfActionDetailList = 'jdCaseOfActionDetailList'
        jdCaseOfActionStats = 'jdCaseOfActionStats'
        jdTitleList = 'jdTitleList'
        jdCaseTypeList = 'jdCaseTypeList'
        jdCaseNumberList = 'jdCaseNumberList'
        jdTypeList = 'jdTypeList'
        jdTrialRoundList = 'jdTrialRoundList'
        jdCourtList = 'jdCourtList'
        jdIdentities = 'jdIdentities'
        hasCourtAnnouncement = 'hasCourtAnnouncement'
        caPublishDateList = 'caPublishDateList'
        openCourtNumber = 'openCourtNumber'
        openCourtPublishDateList = 'openCourtPublishDateList'
        hasOpenCourtSessionAnnc = 'hasOpenCourtSessionAnnc'
        openCourtSessionDateList = 'openCourtSessionDateList'
        openCourtSessionAddressList = 'openCourtSessionAddressList'
        openCourtPublishUnitList = 'openCourtPublishUnitList'
        openCourtRelatedCaseNumberList = 'openCourtRelatedCaseNumberList'
        openCourtContentList = 'openCourtContentList'
        openCourtTitleList = 'openCourtTitleList'
        openCourtCaseStatusList = 'openCourtCaseStatusList'
        openCourtCauses = 'openCourtCauses'
        caTypeList = 'caTypeList'
        caRelatedCaseNumberList = 'caRelatedCaseNumberList'
        caPublishUnitList = 'caPublishUnitList'
        caContentList = 'caContentList'
        caTitleList = 'caTitleList'
        caDateList = 'caDateList'
        caAddressList = 'caAddressList'
        caCaseStatusList = 'caCaseStatusList'
        openCourtIdentities = 'openCourtIdentities'
        has_court_announcement = 'hasCourtAnnouncement'
        has_judgment_document = 'hasJudgmentDocument'
        has_punishment = 'hasPunishment'
        has_open_court_session_annc = 'hasOpenCourtSessionAnnc'  # 有无开庭公告
        has_gov_procurement_illegal = 'hasGovProcurementIllegal'  # 有政府采购严重违法记录
        punishmentCount = 'punishmentCount'
        lastPunishmentDate = 'lastPunishmentDate'
        punishmentTypeList = 'punishmentTypeList'
        mergePunishmentList = 'punishmentList'
        revenuePunishmentList = "revenuePunishmentList"
        taxAbnormalList = 'taxAbnormalList'  # 税务异常类型列表
        dicisionDateList = 'dicisionDateList'  # 行政处罚决定日期
        punishContentList = 'punishContentList'  # 行政处罚内容
        punishAuthorityList = 'punishAuthorityList'  # 行政处罚决定机关
        lastDicisionDate = 'lastDicisionDate'  # 最新行政处罚决定日期
        jd_number = 'jdNumber'  # 裁判文书数量
        create_date = "createDate"
        remove_date = "removeDate"
        jdAnnualCaseStats = 'jdAnnualCaseStats'
        gov_procurement_illegal_list = 'GovProcurementIllegalList'  # 政府采购严重违法记录列表
        calPublishDateList = 'calPublishDateList'

        class JdCaseOfActionListField:
            secondCause = 'secondCause'
            thirdCause = 'thirdCause'

        class JdCaseOfActionStatsField:
            count = 'count'
            type = 'type'

        class JdCaseTypeStatsField:
            count = 'count'
            type = 'type'

        class LatestRiskInfoField:
            riskPubTime = 'riskPubTime'
            riskType = 'riskType'

        class PunishmentListField:
            authority = 'authority'
            basis = 'basis'
            content = 'content'
            decisionDate = 'decisionDate'
            desc = 'desc'
            id = 'id'
            is_deleted = 'is_deleted'
            legalPerson = 'legalPerson'
            name = 'name'
            publishDate = 'publishDate'
            punishmentFileLink = 'punishmentFileLink'
            punishmentType = 'punishmentType'
            result = 'result'
            source = 'source'
            type = 'type'
            updateDate = 'updateDate'

        class RevenuePunishmentListField:
            authority = 'authority'
            basis = 'basis'
            content = 'content'
            decisionDate = 'decisionDate'
            desc = 'desc'
            id = 'id'
            is_deleted = 'is_deleted'
            legalPerson = 'legalPerson'
            name = 'name'
            publishDate = 'publishDate'
            punishmentFileLink = 'punishmentFileLink'
            punishmentType = 'punishmentType'
            result = 'result'
            source = 'source'
            type = 'type'
            updateDate = 'updateDate'

    class TabStatsType:
        risk_judgment_document = 'risk::judgment_document'
        risk_court_announcement = 'risk::court_announcement'
        risk_open_court_announcement = 'risk::open_court_announcement'
        risk_punishment = 'risk::punishment'

    class AnomalyList:
        create_reason = "createReason"
        create_date = "createDate"
        create_authority = "createAuthority"
        remove_reason = "removeReason"
        remove_date = "removeDate"
        remove_authority = "removeAuthority"
        sort_key = 'sort_key'

    INPUTS = [OdsEnterpriseAnomaly, DwdEnterprise, DwmEnterpriseJudgmentDocument, DwmEnterpriseCourtAnnouncement,
              OdsCombinedPunishment, OdsEnforcement, OdsBankruptcyReorganization, OdsPunishDishonesty]

    SPARK_SCHEMA = {
        "fields": [
            {
                "metadata": {

                },
                "name": "GovProcurementIllegalList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "_id",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "create_time",
                                "nullable": True,
                                "type": "timestamp"
                            },
                            {
                                "metadata": {

                                },
                                "name": "import_update_time",
                                "nullable": True,
                                "type": "timestamp"
                            },
                            {
                                "metadata": {

                                },
                                "name": "last_update_time",
                                "nullable": True,
                                "type": "timestamp"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameId",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "pdAddress",
                                "nullable": True,
                                "type": {
                                    "fields": [
                                        {
                                            "metadata": {

                                            },
                                            "name": "city",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "district",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "province",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "subdistrict",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "value",
                                            "nullable": True,
                                            "type": "string"
                                        }
                                    ],
                                    "type": "struct"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "pdCompany",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "pdCreditCode",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "pdLawEnforcementAgency",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "pdPublishDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "pdPunishBasis",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "pdPunishDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "pdPunishReason",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "pdPunishResult",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "pdSource",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "pdStatus",
                                "nullable": True,
                                "type": "long"
                            },
                            {
                                "metadata": {

                                },
                                "name": "pdType",
                                "nullable": True,
                                "type": "long"
                            },
                            {
                                "metadata": {

                                },
                                "name": "pdUrl",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "update_time",
                                "nullable": True,
                                "type": "timestamp"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "_id",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "annualRiskCount",
                "nullable": True,
                "type": {
                    "keyType": "string",
                    "type": "map",
                    "valueContainsNull": True,
                    "valueType": "long"
                }
            },
            {
                "metadata": {

                },
                "name": "annualRiskStats",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "count",
                                "nullable": True,
                                "type": "long"
                            },
                            {
                                "metadata": {

                                },
                                "name": "type",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "year",
                                "nullable": True,
                                "type": "long"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "annualRiskStatsForEt",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "count",
                                "nullable": True,
                                "type": "long"
                            },
                            {
                                "metadata": {

                                },
                                "name": "type",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "year",
                                "nullable": True,
                                "type": "long"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "caCaseStatusList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "caContentList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "caDateList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "caPublishDateList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "caPublishUnitList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "caRelatedCaseNumberList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "caTitleList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "caTypeList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "caAddressList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "calPublishDateList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "dicisionDateList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "hasCourtAnnouncement",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {

                },
                "name": "hasGovProcurementIllegal",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {

                },
                "name": "hasJudgmentDocument",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {

                },
                "name": "hasOpenCourtSessionAnnc",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {

                },
                "name": "hasPunishment",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {

                },
                "name": "jdAnnualCaseStats",
                "nullable": True,
                "type": {
                    "keyType": "long",
                    "type": "map",
                    "valueContainsNull": True,
                    "valueType": "long"
                }
            },
            {
                "metadata": {

                },
                "name": "jdCaseNumberList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "jdCaseOfActionDetailList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "jdCaseOfActionList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "secondCause",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "thirdCause",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "jdCaseOfActionStats",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "count",
                                "nullable": True,
                                "type": "long"
                            },
                            {
                                "metadata": {

                                },
                                "name": "type",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "jdCaseTypeList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "jdCaseTypeStats",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "count",
                                "nullable": True,
                                "type": "long"
                            },
                            {
                                "metadata": {

                                },
                                "name": "type",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "jdCourtList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "jdIdentities",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "jdNumber",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {

                },
                "name": "jdPublishDateList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "jdPublishYearList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "jdRoleStats",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "ratio",
                                "nullable": True,
                                "type": "double"
                            },
                            {
                                "metadata": {

                                },
                                "name": "type",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "jdRoleStatsNew",
                "nullable": True,
                "type": {
                    "keyType": "string",
                    "type": "map",
                    "valueContainsNull": True,
                    "valueType": "long"
                }
            },
            {
                "metadata": {

                },
                "name": "jdThirdCauseList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "jdTitleList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "jdTrialRoundList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "jdTypeList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "lastDicisionDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "lastPunishmentDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "latestRiskInfo",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "riskPubTime",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "riskType",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "openCourtCauses",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "openCourtContentList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "openCourtIdentities",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "openCourtNumber",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {

                },
                "name": "openCourtPublishDateList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "openCourtPublishUnitList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "openCourtRelatedCaseNumberList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "openCourtSessionAddressList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "openCourtSessionDateList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "openCourtTitleList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "punishAuthorityList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "punishContentList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "punishmentCount",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {

                },
                "name": "punishmentList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "_id",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "authority",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "content",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "decisionDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "id",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "publishDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "punishmentFileLink",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "reason",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "result",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "sources",
                                "nullable": True,
                                "type": {
                                    "containsNull": True,
                                    "elementType": "string",
                                    "type": "array"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "type",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "punishmentTypeList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "revenuePunishmentList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "_id",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "authority",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "content",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "decisionDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "id",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "publishDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "punishmentFileLink",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "reason",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "result",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "sources",
                                "nullable": True,
                                "type": {
                                    "containsNull": True,
                                    "elementType": "string",
                                    "type": "array"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "type",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "riskTypeCount",
                "nullable": True,
                "type": {
                    "keyType": "string",
                    "type": "map",
                    "valueContainsNull": True,
                    "valueType": "long"
                }
            },
            {
                "metadata": {

                },
                "name": "riskTypeStats",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "ratio",
                                "nullable": True,
                                "type": "double"
                            },
                            {
                                "metadata": {

                                },
                                "name": "type",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "riskTypeStatsForEt",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "ratio",
                                "nullable": True,
                                "type": "double"
                            },
                            {
                                "metadata": {

                                },
                                "name": "type",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "tabStats",
                "nullable": True,
                "type": {
                    "keyType": "string",
                    "type": "map",
                    "valueContainsNull": True,
                    "valueType": "long"
                }
            },
            {
                "metadata": {

                },
                "name": "taxAbnormalList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            }
        ],
        "type": "struct"
    }
