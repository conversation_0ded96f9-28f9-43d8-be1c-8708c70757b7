#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    ~~~~~~~~~~~~~~~~~~~~~~~
    司法信息计算
    Description of this file

    :author: l<PERSON><PERSON><PERSON>
    :copyright: (c) process_dwm_enterprise_court_announcement.py, Tungee
    :date created: 2022/12/8
    :python version: 3.6

"""
import click

from tdr.warehouse.common.processor.warehouse_processor import ProcessType
from tdr.warehouse.common.warehouse import EnterpriseWarehouse
from tdr.warehouse.dwm.enterprise.dwm_enterprise_court_announcement.meta.dwm_enterprise_court_announcement import \
    DwmEnterpriseCourtAnnouncement


@click.command()
@click.option('--version')
@click.option('--config')
@click.option('--driver-memory')
@click.option('--executor-cores')
@click.option('--num-executors')
@click.option('--executor-memory')
@click.option('--parallelism')
@click.option('--queue')
@click.option('--jars')
@click.option('--n_partition', default='16')
@click.option('--use_lakehouse', type=bool, default=True)
@click.option("--conf", "-c", default=None, multiple=True)
@click.option('--process_type', default=ProcessType.SPARK.value)
@click.option('--clickzetta_version', default=None, type=str, help='当 process_type=clickzetta 时，此参数为必填，用于支持导出哪一个版本的表')
def main(version, config, n_partition, **kwargs):
    EnterpriseWarehouse(config).build(DwmEnterpriseCourtAnnouncement, version, n_partition, **kwargs)


if __name__ == '__main__':
    main()
