#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    ~~~~~~~~~~~~~~~~~~~~~~~

    Description of this file

    :author: <PERSON><PERSON><PERSON><PERSON>
    :copyright: (c) dwm_enterprise_court_announcement_compute.py, Tungee
    :date created: 2022/12/8
    :python version: 3.6

"""
import sys

from pyspark import StorageLevel

from tdr.common.constant.module import ModuleNameConstant
from tdr.common.utils.spark.spark_utils import *

from tdr.warehouse.dwm.enterprise.dwm_enterprise_court_announcement.utils.dwm_court_announcement_compute_util import *
from tdr.warehouse.utils.spark import get_rdd_from_file

JOB_NAME = 'DW_{}_{}'.format(ModuleNameConstant.enterprise, DwmEnterpriseCourtAnnouncement.NAME)


def main(*args):
    """ 企业经营风险数据计算入口
    """
    [
        ods_court_announcement_input,
        dwm_court_announcement,
        n_partition
    ] = args

    spark = SparkSession.builder.appName(JOB_NAME).getOrCreate()
    sc = spark.sparkContext
    n_partition = int(n_partition)

    court_announcement_ods_rdd = get_rdd_from_file(sc, ods_court_announcement_input)

    # 法院公告过滤去重
    ca_rdd = court_announcement_ods_rdd.filter(
        filter_court_announcement_without_name_id
    ).map(
        map_court_announcement_with_ca_url
    ).reduceByKey(
        reduce_court_announcement_by_ca_url
    ).values().map(
        court_announcement_by_ca_url
    )

    # ==================开庭公告数据处理==================
    # 开庭公告过滤去重
    base_open_court_rdd = court_announcement_ods_rdd.filter(
        filter_open_court_announcement_without_name_id
    ).map(
        preprocess_open_court
    )

    # 开庭公告第一轮去重
    # base_open_court_rdd.persist(StorageLevel.MEMORY_AND_DISK)
    # first_round_rdd1 = base_open_court_rdd.filter(first_round_check)
    # first_round_rdd2 = base_open_court_rdd.filter(lambda doc: not first_round_check(doc))
    # first_round_rdd1 = first_round_rdd1.map(map_first_round_dup_key).groupByKey().flatMap(
    #     lambda data: open_court_duplicate(data, func=is_duplicate)
    # ).map(clean_process)
    # first_round_open_court_rdd = first_round_rdd1.union(first_round_rdd2)

    # 开庭公告第二轮去重
    base_open_court_rdd.persist(StorageLevel.MEMORY_AND_DISK)
    second_round_rdd1 = base_open_court_rdd.filter(lambda doc: doc.get('clean_case_number'))
    second_round_rdd2 = base_open_court_rdd.filter(lambda doc: not doc.get('clean_case_number'))
    second_round_rdd1 = second_round_rdd1.map(lambda doc: (doc['clean_case_number'], doc)).groupByKey().flatMap(
        lambda data: open_court_duplicate(data, func=is_duplicate_2)
    ).map(clean_process)
    second_round_open_court_rdd = second_round_rdd1.union(second_round_rdd2)

    # 开庭公告第三轮去重
    second_round_open_court_rdd.persist(StorageLevel.MEMORY_AND_DISK)
    third_round_rdd1 = second_round_open_court_rdd.filter(third_round_check)
    third_round_rdd2 = second_round_open_court_rdd.filter(lambda doc: not third_round_check(doc))
    third_round_rdd1 = third_round_rdd1.map(map_third_round_dup_key).groupByKey().flatMap(
        lambda data: open_court_duplicate(data, func=is_duplicate_3)
    )
    third_round_open_court_rdd = third_round_rdd1.union(third_round_rdd2)

    # 开庭公告第四轮去重
    third_round_open_court_rdd.persist(StorageLevel.MEMORY_AND_DISK)
    fourth_round_rdd1 = third_round_open_court_rdd.filter(fourth_round_check)
    fourth_round_rdd2 = third_round_open_court_rdd.filter(lambda doc: not fourth_round_check(doc))
    fourth_round_rdd1 = fourth_round_rdd1.map(map_fourth_round_dup_key).groupByKey().flatMap(
        lambda data: open_court_duplicate(data, func=is_duplicate_4)
    )
    fourth_round_open_court_rdd = fourth_round_rdd1.union(fourth_round_rdd2)

    open_ca_rdd = fourth_round_open_court_rdd.map(
        map_open_court_announcement_with_ca_url
    ).reduceByKey(
        lambda x, y: x + y
    ).values().map(
        open_court_announcement_by_ca_url
    ).map(case_member_process)

    ca_rdd = ca_rdd.union(open_ca_rdd)

    persist_rdd(ca_rdd, dwm_court_announcement, numPartitions=n_partition)


if __name__ == '__main__':
    main(*sys.argv[1:])
