# -*- coding: utf-8 -*-
"""
    dwm_enterprise_court_announcement
    ~~~~~~~

    Description

    :author: <PERSON>
    :copyright: (c) 2023, Tungee
    :date created: 2023-01-10
    :python version:
"""
from tdr.common.constant.common import FormatType
from tdr.warehouse.ods.ods_court_announcement.meta.ods_court_announcement import OdsCourtAnnouncement


class DwmEnterpriseCourtAnnouncement(object):
    NAME = 'dwm_enterprise_court_announcement'

    FORMAT_TYPE = FormatType.json

    class Field:
        _id = '_id'
        caAddress = 'caAddress'
        caCaseReason = 'caCaseReason'
        caCaseStatus = 'caCaseStatus'
        caCaseType = 'caCaseType'
        caCcaPlaintiffList = 'caCcaPlaintiffList'
        caContent = 'caContent'
        caDate = 'caDate'
        caDealGrade = 'caDealGrade'
        caDefendant = 'caDefendant'
        caDeleted = 'caDeleted'
        caDepartment = 'caDepartment'
        caEndDate = 'caEndDate'
        caJudgeAssistant = 'caJudgeAssistant'
        caJudgeInfo = 'caJudgeInfo'
        caLitigaDate = 'caLitigaDate'
        caLitigantList = 'caLitigantList'
        caRelationList = 'caRelationList'
        caPlaintiffList = 'caPlaintiffList'
        caPublishDate = 'caPublishDate'
        caPublishPage = 'caPublishPage'
        caPublishUnit = 'caPublishUnit'
        caPublishUnitdress = 'caPublishUnitdress'
        caPublisitigantList = 'caPublisitigantList'
        caRegion = 'caRegion'
        caRelatedCaseNumber = 'caRelatedCaseNumber'
        caSource = 'caSource'
        caSourceIndex = 'caSourceIndex'
        caStartDate = 'caStartDate'
        caThird = 'caThird'
        caTitle = 'caTitle'
        caType = 'caType'
        caUrl = 'caUrl'
        calPublishDate = 'calPublishDate'  # 发布日期
        create_time = 'create_time'
        import_update_time = 'import_update_time'
        last_update_time = 'last_update_time'
        update_time = 'update_time'

        class CaCcaPlaintiffListField:
            name = 'name'

        class CaDefendantField:
            name = 'name'
            nameId = 'nameId'
            nameIdSource = 'nameIdSource'

            class NameIdSourceField:
                name = 'name'
                nameId = 'nameId'
                source = 'source'

        class CaJudgeInfoField:
            judgeName = 'judgeName'
            judgePhone = 'judgePhone'

        class CaLitigantListField:
            name = 'name'
            nameId = 'nameId'
            nameIdSource = 'nameIdSource'

            class NameIdSourceField:
                name = 'name'
                nameId = 'nameId'
                source = 'source'

        class CaPlaintiffListField:
            name = 'name'
            nameId = 'nameId'
            nameIdSource = 'nameIdSource'

            class NameIdSourceField:
                name = 'name'
                nameId = 'nameId'
                source = 'source'

        class CaRelationListField:
            name = 'name'
            nameId = 'nameId'
            nameIdSource = 'nameIdSource'

            class NameIdSourceField:
                name = 'name'
                nameId = 'nameId'
                source = 'source'

        class CaPublisitigantListField:
            name = 'name'

        class CaRegionField:
            value = 'value'

    INPUTS = [OdsCourtAnnouncement]

    SPARK_SCHEMA = {
        "fields": [
            {
                "metadata": {

                },
                "name": "_id",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caAddress",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caCaseReason",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caCaseStatus",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caCaseType",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caContent",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caDealGrade",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caDefendant",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "name",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameId",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameIdSource",
                                "nullable": True,
                                "type": {
                                    "fields": [
                                        {
                                            "metadata": {

                                            },
                                            "name": "name",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "nameId",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "source",
                                            "nullable": True,
                                            "type": "string"
                                        }
                                    ],
                                    "type": "struct"
                                }
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "caDeleted",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caDepartment",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caEndDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caJudgeAssistant",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caJudgeInfo",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "caJudgeName",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "judgeName",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "judgePhone",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "caLitigantList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "name",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameId",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameIdSource",
                                "nullable": True,
                                "type": {
                                    "fields": [
                                        {
                                            "metadata": {

                                            },
                                            "name": "name",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "nameId",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "source",
                                            "nullable": True,
                                            "type": "string"
                                        }
                                    ],
                                    "type": "struct"
                                }
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "caPlaintiffList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "name",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameId",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameIdSource",
                                "nullable": True,
                                "type": {
                                    "fields": [
                                        {
                                            "metadata": {

                                            },
                                            "name": "name",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "nameId",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "source",
                                            "nullable": True,
                                            "type": "string"
                                        }
                                    ],
                                    "type": "struct"
                                }
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "caPartiesList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "name",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameId",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameIdSource",
                                "nullable": True,
                                "type": {
                                    "fields": [
                                        {
                                            "metadata": {

                                            },
                                            "name": "name",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "nameId",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "source",
                                            "nullable": True,
                                            "type": "string"
                                        }
                                    ],
                                    "type": "struct"
                                }
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "caPublishDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caPublishPage",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caPublishUnit",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caRegion",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "city",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "district",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "name",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "province",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "subdistrict",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "value",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "caRelatedCaseNumber",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caRelationList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "name",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameId",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameIdSource",
                                "nullable": True,
                                "type": {
                                    "fields": [
                                        {
                                            "metadata": {

                                            },
                                            "name": "name",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "nameId",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "source",
                                            "nullable": True,
                                            "type": "string"
                                        }
                                    ],
                                    "type": "struct"
                                }
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "caServiceDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caSource",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caStartDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caThird",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caThirdList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "name",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameId",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameIdSource",
                                "nullable": True,
                                "type": {
                                    "fields": [
                                        {
                                            "metadata": {

                                            },
                                            "name": "name",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "nameId",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "source",
                                            "nullable": True,
                                            "type": "string"
                                        }
                                    ],
                                    "type": "struct"
                                }
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "caTitle",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caType",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caUrl",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "calPublishDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "court_deleted",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {

                },
                "name": "court_notice_deleted",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {

                },
                "name": "create_time",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "import_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "last_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "originCaUrl",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "update_time",
                "nullable": True,
                "type": "timestamp"
            }
        ],
        "type": "struct"
    }
