#!/usr/bin/env python
# -*- coding: utf-8 -*-
import re
from collections import defaultdict
from datetime import datetime, timedelta

from pyspark.resultiterable import ResultIterable
from sklearn.feature_extraction.text import CountVectorizer
from sklearn.metrics.pairwise import cosine_similarity

from tdr.common.utils.md5_util import str_md5_base64
from tdr.common.utils.time_helper import get_monday_str
from tdr.warehouse.dwm.enterprise.dwm_enterprise_court_announcement.meta.dwm_enterprise_court_announcement import \
    DwmEnterpriseCourtAnnouncement
from tdr.warehouse.dwm.enterprise.dwm_enterprise_court_announcement.utils.constant import *
from tdr.warehouse.ods.ods_court_announcement.meta.ods_court_announcement import OdsCourtAnnouncement

HOLDER_NAME_MAPPING = {}
DATE_STRING_MATCH = re.compile(r'^\d{4}-\d{2}-\d{2}$')
TWO_YEARS_LATER = (datetime.today() + timedelta(days=365 * 2)).strftime('%Y-%m-%d')
MIN_CONTENT_LEN = 25
SIMILARITY_THRESHOLD = 0.95
# 案件相关人员字段
RELATION_MEMBER_FIELDS = ['caLitigantList', 'caPlaintiffList', 'caDefendant', 'caThirdList']


def filter_court_announcement_without_name_id(doc):
    for main_key in [OdsCourtAnnouncement.Field.caSource, OdsCourtAnnouncement.Field.caUrl]:
        if not doc.get(main_key):
            return False
    if not any([doc.get(i) for i in
                [OdsCourtAnnouncement.Field.caRelatedCaseNumber, OdsCourtAnnouncement.Field.caContent,
                 OdsCourtAnnouncement.Field.caType]]):
        return False
    if doc[OdsCourtAnnouncement.Field.caSource] not in CA_SOURCE_PRIORITY_LIST:
        return False
    map_list = doc.get(OdsCourtAnnouncement.Field.caLitigantList, []) + doc.get(
        OdsCourtAnnouncement.Field.caPlaintiffList, [])
    if not any([i.get('nameId') for i in map_list]):
        return False
    # 法院公告剔除：开庭公告
    if doc.get(OdsCourtAnnouncement.Field.caType) == OPEN_CA_SOURCE_TYPE:
        return False
    return True


def map_court_announcement_with_ca_url(doc):
    ca_content = doc.get(OdsCourtAnnouncement.Field.caContent, '')
    ca_content = re.sub(r'（|）|，|；|：|\(|\)|,|;|:|。|\.|"|“|”', '', ca_content)
    ca_dup_key = str_md5_base64(doc.get(OdsCourtAnnouncement.Field.caRelatedCaseNumber, '') +
                                ca_content + doc.get(OdsCourtAnnouncement.Field.caType, ''), digit=16)
    doc['ca_dup_key'] = ca_dup_key
    return ca_dup_key, doc


def reduce_court_announcement_by_ca_url(x_data, y_data):
    x_source, y_source = x_data[OdsCourtAnnouncement.Field.caSource], y_data[OdsCourtAnnouncement.Field.caSource]
    if CA_SOURCE_PRIORITY_LIST.index(y_source) < CA_SOURCE_PRIORITY_LIST.index(x_source):
        return y_data
    return x_data


def filter_open_court_announcement_without_name_id(doc):
    for main_key in [
        OdsCourtAnnouncement.Field.caSource,
        OdsCourtAnnouncement.Field.caUrl,
        # OdsCourtAnnouncement.Field.caDate
    ]:
        if not doc.get(main_key):
            return False
    if all([not doc.get(f) for f in [
        OdsCourtAnnouncement.Field.caDate,
        OdsCourtAnnouncement.Field.caRelatedCaseNumber,
        OdsCourtAnnouncement.Field.caCaseReason,
        OdsCourtAnnouncement.Field.caPublishUnit]]):
        return False
    if doc[OdsCourtAnnouncement.Field.caSource] not in set(OPEN_CA_SOURCE_PRIORITY_LIST):
        return False
    map_list = doc.get(OdsCourtAnnouncement.Field.caLitigantList, []) + doc.get(
        OdsCourtAnnouncement.Field.caPlaintiffList, []) + doc.get(OdsCourtAnnouncement.Field.caThirdList, []) \
               + doc.get(OdsCourtAnnouncement.Field.caDefendant, [])
    nameIds = {i['nameId'] for i in map_list if i.get('nameId')}
    if len(nameIds) == 0 or len(nameIds) > 50:
        return False
    if doc.get(OdsCourtAnnouncement.Field.caType) not in OPEN_COURT_TYPE_SET:
        return False
    if doc.get('is_deleted'):
        return False
    return True


def judge_defendant_plaintiff_data(ca_plaintiff_list, ca_litigant_list, ca_defendant_list, ca_third_list):
    ca_plaintiff_name_set = {val.get('name') for val in ca_plaintiff_list if val and val.get('name')}  # 原告人
    ca_litigant_name_set = {val.get('name') for val in ca_litigant_list if val and val.get('name')}  # 当事人
    ca_defendant_name_set = {val.get('name') for val in ca_defendant_list if val and val.get('name')}  # 被告人
    ca_third_name_set = {val.get('name') for val in ca_third_list if val and val.get('name')}  # 第三人
    new_ca_plaintiff_list, new_ca_litigant_list = [], []
    if ca_litigant_list:
        if ca_plaintiff_list:
            # 有：当事人、原告
            real_litigant_name_set = ca_litigant_name_set - ca_plaintiff_name_set - ca_third_name_set
            # 【计算被告】
            if real_litigant_name_set:
                # 经过当事人-原告-【第三人】后有数据
                for val in ca_litigant_list:
                    if val.get('name') in real_litigant_name_set:
                        new_ca_litigant_list.append(val)
            elif ca_defendant_list:
                # 有：被告人
                new_ca_litigant_list = ca_defendant_list
            # 【计算原告】
            new_ca_plaintiff_list = ca_plaintiff_list
        else:
            # 有：当事人  无：原告
            if ca_defendant_list:
                # 【计算被告】
                new_ca_litigant_list = ca_defendant_list
                real_plaintiff_name_set = ca_litigant_name_set - ca_defendant_name_set - ca_third_name_set
                # 【计算原告】
                if real_plaintiff_name_set:
                    # 经过当事人-被告-【第三人】后有数据
                    for val in ca_litigant_list:
                        if val.get('name') in real_plaintiff_name_set:
                            new_ca_plaintiff_list.append(val)
    else:
        if ca_plaintiff_list and ca_defendant_list:
            # 有：原告、被告、【第三人】  无：当事人
            new_ca_litigant_list = ca_defendant_list
            new_ca_plaintiff_list = ca_plaintiff_list
        elif ca_plaintiff_list and not ca_defendant_list:
            # 有：原告、【第三人】  无：当事人、被告
            new_ca_plaintiff_list = ca_plaintiff_list
        elif not ca_plaintiff_list and ca_defendant_list:
            # 有：被告、【第三人】  无：当事人、原告
            new_ca_litigant_list = ca_defendant_list
    return new_ca_plaintiff_list, new_ca_litigant_list


def union_all_relation_list(ca_plaintiff_list, ca_litigant_list, ca_defendant_list, ca_third_list):
    relation_list, relation_key_set, charge_list = [], set(), []
    if ca_litigant_list and isinstance(ca_litigant_list, list):
        charge_list.extend(ca_litigant_list)
    if ca_plaintiff_list and isinstance(ca_plaintiff_list, list):
        charge_list.extend(ca_plaintiff_list)
    if ca_defendant_list and isinstance(ca_defendant_list, list):
        charge_list.extend(ca_defendant_list)
    if ca_third_list and isinstance(ca_third_list, list):
        charge_list.extend(ca_third_list)
    for val in charge_list:
        key = '%s-%s' % (val.get(DwmEnterpriseCourtAnnouncement.Field.CaRelationListField.name, ''),
                         val.get(DwmEnterpriseCourtAnnouncement.Field.CaRelationListField.nameId))
        if key and key not in relation_key_set:
            relation_key_set.add(key)
            relation_list.append(val)
    return relation_list


def map_open_court_announcement_with_ca_url(doc):
    # 去重主键：caRelatedCaseNumber + caRelationList.nameId（去除只在第三人列表中的nameId） + caDate
    ca_related_case_number = doc.get(OdsCourtAnnouncement.Field.caRelatedCaseNumber)
    ca_plaintiff_list = doc.get(OdsCourtAnnouncement.Field.caPlaintiffList)
    ca_litigant_list = doc.get(OdsCourtAnnouncement.Field.caLitigantList)
    ca_defendant_list = doc.get(OdsCourtAnnouncement.Field.caDefendant)

    ca_date = doc.get(OdsCourtAnnouncement.Field.caDate)
    ca_date = ca_date[:10] if ca_date else ''

    # 所有关联人去除只在第三人列表的nameId
    ca_relation_list = union_all_relation_list(ca_plaintiff_list, ca_litigant_list, ca_defendant_list, [])
    if ca_related_case_number:
        all_name_id_list = list(set([each['nameId'] for each in ca_relation_list if each.get('nameId')]))
        if all_name_id_list:
            all_name_id_list.sort()
            all_name_id_list = '_'.join(all_name_id_list)
        else:
            all_name_id_list = doc.get('_id')
        key = str_md5_base64(ca_related_case_number + all_name_id_list + ca_date, digit=16)
    else:
        all_name_list = list(set([each['name'] for each in ca_relation_list if each.get('name')]))
        if all_name_list:
            all_name_list.sort()
            all_name_list = '_'.join(all_name_list)
        else:
            all_name_list = doc.get('_id')
        key = str_md5_base64(all_name_list + ca_date, digit=16)
    doc['open_dup_key'] = key
    return key, [doc]


def open_court_announcement_by_ca_url(data_list):
    def compare_key(x):
        num = len(x.get('caLitigantList', []))
        hasCaPublishUnit = 1 if x.get('caPublishUnit') else 0
        hasCaCaseReason = 1 if x.get('caCaseReason') else 0
        caDate = x.get('caDate') or ''
        return num, hasCaPublishUnit, hasCaCaseReason, caDate

    if data_list and isinstance(data_list, list) and len(list(data_list)) > 0:
        # for data_val in data_list:
        #     data_val['caSourceIndex'] = OPEN_CA_SOURCE_PRIORITY_LIST.index(
        #         data_val[OdsCourtAnnouncement.Field.caSource])
        # data_list.sort(key=lambda x: x['caSourceIndex'] if x.get('caSourceIndex') else -1)

        data_list.sort(key=compare_key, reverse=True)
        # 判断优先级第一的是否有第三人列表
        first_val = data_list[0]
        # 计算发布日期
        caPublishDate = first_val.get(OdsCourtAnnouncement.Field.caPublishDate)
        caDate = first_val.get(OdsCourtAnnouncement.Field.caDate)
        create_time = first_val.get(OdsCourtAnnouncement.Field.create_time)
        if isinstance(create_time, str) and DATE_STRING_MATCH.search(create_time[:10]):
            create_date = create_time[:10]
        else:
            create_date = create_time.strftime('%Y-%m-%d')
        if not caPublishDate and caDate and create_date < caDate:
            caPublishDate = create_date
        if caPublishDate:
            first_val[DwmEnterpriseCourtAnnouncement.Field.calPublishDate] = caPublishDate
        if first_val.get(OdsCourtAnnouncement.Field.caThirdList) is None:
            ca_third_list = None
            for data_val in data_list:
                if data_val.get(OdsCourtAnnouncement.Field.caThirdList):
                    ca_third_list = data_val.get(OdsCourtAnnouncement.Field.caThirdList)
                    break
            if ca_third_list:
                first_val[OdsCourtAnnouncement.Field.caThirdList] = ca_third_list

        # 起诉人列表
        ca_plaintiff_list = first_val.get(OdsCourtAnnouncement.Field.caPlaintiffList, [])
        # 当事人列表
        ca_litigant_list = first_val.get(OdsCourtAnnouncement.Field.caLitigantList, [])
        # 被告人列表
        ca_defendant_list = first_val.get(OdsCourtAnnouncement.Field.caDefendant, [])
        # 第三人列表
        ca_third_list = first_val.get(OdsCourtAnnouncement.Field.caThirdList, [])

        new_ca_plaintiff_list, new_ca_litigant_list = judge_defendant_plaintiff_data(ca_plaintiff_list,
            ca_litigant_list,
            ca_defendant_list, ca_third_list)

        ca_relation_list = union_all_relation_list(ca_plaintiff_list,
            ca_litigant_list,
            ca_defendant_list, ca_third_list)

        # 对当事人及起诉人列表重新赋值
        first_val[DwmEnterpriseCourtAnnouncement.Field.caPlaintiffList] = new_ca_plaintiff_list
        first_val[DwmEnterpriseCourtAnnouncement.Field.caLitigantList] = new_ca_litigant_list
        first_val[DwmEnterpriseCourtAnnouncement.Field.caRelationList] = ca_relation_list
        # 重新定义主键
        first_val['source_id'] = first_val['_id']
        first_val['_id'] = str_md5_base64('%s_%s' % (first_val['_id'], '开庭公告'), digit=16)
        return first_val


def court_announcement_by_ca_url(doc):
    # 起诉人列表
    ca_plaintiff_list = doc.get(OdsCourtAnnouncement.Field.caPlaintiffList, [])
    # 当事人列表
    ca_litigant_list = doc.get(OdsCourtAnnouncement.Field.caLitigantList, [])
    # 被告人列表
    ca_defendant_list = doc.get(OdsCourtAnnouncement.Field.caDefendant, [])
    # 第三人列表
    ca_third_list = doc.get(OdsCourtAnnouncement.Field.caThirdList, [])
    # 所有涉及关联人集合
    ca_relation_list = union_all_relation_list(ca_plaintiff_list,
        ca_litigant_list,
        ca_defendant_list, ca_third_list)
    doc[DwmEnterpriseCourtAnnouncement.Field.caRelationList] = ca_relation_list
    # 重新定义主键
    doc['source_id'] = doc['_id']
    doc['_id'] = str_md5_base64('%s_%s' % (doc['_id'], '法院公告'), digit=16)
    return doc


def case_number_clean(case_number):
    if not case_number:
        return ''
    return re.sub(r'[年\(\)（）\s+]', '', re.sub(r'[^\w\s+]', '', case_number))


def publish_unit_clean(unit):
    if not unit:
        return ''
    unit = unit.replace('地区', '').replace('自治', '')
    return re.sub(r'[省市区县州\(\)（）\s+]', '', re.sub(r'[^\w\s+]', '', unit))


def extract_ca_content(content) -> str:
    """
    公告正文特征提取
    :param content:
    :return:
    """

    def _remove_attachment(s):
        # 去除文本出现的“附件”字符信息
        keywords = [
            "zip", "rar", "7z", "tar", "gz", "bz2", "csv", "txt",
            "docx", "doc", "pdf", "html", "json", "ppt", "pptx", "xls",
            "xlsx", "md", "tex", "odt", "rtf", "jpg", "jpeg", "png",
            "tiff", "tif"
        ]
        s = re.sub('|'.join(keywords), '', s)
        return s

    content = content.lower()
    content = re.sub(r'https?\:\/\/\w*\.\w*\.[\w\/\=\#\?\_\-&\.]*', '', content)  # 去除网址
    content = re.sub(r'|'.join(HTML_SYMBOLS), '', content)  # 去除HTML字符
    content = re.sub(r'[^\w\s+]', '', content)  # 去除标点符号
    content = _remove_attachment(content)  # 去除`附件字符`
    text = re.sub('\s+', ' ', ' '.join(re.compile(r'\d*|[a-z]*').findall(content)))
    return text


def preprocess_open_court(doc):
    """
    开庭公告预处理
    :param doc:
    :return:
    """
    ca_content = doc.get(OdsCourtAnnouncement.Field.caContent)
    if ca_content:
        if len(ca_content) < 10:
            doc.pop(OdsCourtAnnouncement.Field.caContent)
        # else:
        #     extract_content = extract_ca_content(ca_content)
        #     doc['extractContent'] = extract_content
    ca_publish_unit = doc.get(OdsCourtAnnouncement.Field.caPublishUnit) or ''
    if ca_publish_unit:
        if len(ca_publish_unit) > 35:
            doc.pop(OdsCourtAnnouncement.Field.caPublishUnit)
        else:
            doc['clean_publish_unit'] = publish_unit_clean(ca_publish_unit)
    ca_related_case_number = doc.get(OdsCourtAnnouncement.Field.caRelatedCaseNumber)
    if ca_related_case_number:
        doc['clean_case_number'] = case_number_clean(ca_related_case_number)
    ca_date = doc.get(OdsCourtAnnouncement.Field.caDate)
    if ca_date:
        doc['clean_date'] = ca_date[:10]
    ca_date = doc.get(OdsCourtAnnouncement.Field.caDate)
    if ca_date and ca_date > TWO_YEARS_LATER:
        # 开庭公告的开庭日期大于当前年份2年的日期都置为空
        doc.pop(OdsCourtAnnouncement.Field.caDate)
    ca_case_reason = doc.get(OdsCourtAnnouncement.Field.caCaseReason) or ''
    if len(ca_case_reason) > 45:
        doc.pop(OdsCourtAnnouncement.Field.caCaseReason)
    # 去除无效字段
    for each in ['caLitigantList', 'caPlaintiffList', 'caDefendant', 'caThirdList', 'caJudgeInfo']:
        if doc.get(each) is not None and len(doc.get(each)) == 0:
            doc.pop(each)
    return doc


def clean_process(doc):
    ca_related_case_number = doc.get(OdsCourtAnnouncement.Field.caRelatedCaseNumber)
    if ca_related_case_number and doc.get('clean_case_number') is None:
        doc['clean_case_number'] = case_number_clean(ca_related_case_number)
    ca_publish_unit = doc.get(OdsCourtAnnouncement.Field.caPublishUnit) or ''
    if ca_publish_unit and doc.get('clean_publish_unit') is None:
        doc['clean_publish_unit'] = publish_unit_clean(ca_publish_unit)
    return doc


def first_round_check(doc):
    """
    公告日期、公告法院、公告内容校验
    :param doc:
    :return:
    """
    return doc.get(OdsCourtAnnouncement.Field.caPublishDate) and \
        doc.get(OdsCourtAnnouncement.Field.caPublishUnit) and \
        doc.get('extractContent') and len(doc['extractContent']) > MIN_CONTENT_LEN


def map_first_round_dup_key(doc):
    ca_publish_date = doc[OdsCourtAnnouncement.Field.caPublishDate]
    monday_str = get_monday_str(ca_publish_date)
    return (monday_str, doc['clean_publish_unit']), doc


def open_court_duplicate(data, func=None):
    _, doc_iter = data
    assert isinstance(doc_iter, ResultIterable)
    docs = doc_iter.data
    _len = doc_iter.maxindex
    assert isinstance(docs, list)
    if _len == 1:
        return docs
    if func == is_duplicate_4:
        for doc in docs[::-1]:
            if not doc.get('clean_case_number') and not doc.get('clean_publish_unit'):
                # 直接丢弃，不参与去重比较
                docs.remove(doc)
                _len -= 1
        if _len == 1:
            return docs
    docs.sort(key=lambda x: (-OPEN_CA_SOURCE_PRIORITY_LIST.index(x['caSource']), x.get('update_time')), reverse=True)
    duplicate_ids = set()  # 去重掉的数据_id集合
    # 两两比较判断是否重复，并补全缺失字段值
    for i in range(1, _len):
        doc_i = docs[i]
        _id_i = doc_i['_id']
        for j in range(i):
            doc_j = docs[j]
            _id_j = doc_j['_id']
            if _id_i in duplicate_ids:
                break
            if _id_j in duplicate_ids:
                continue
            is_dup = func(doc_i, doc_j)
            if is_dup:
                dup_id = dup_process(doc_i, doc_j)
                duplicate_ids.add(dup_id)
    if duplicate_ids:
        for doc in docs[::-1]:
            if doc['_id'] in duplicate_ids:
                docs.remove(doc)
        return docs
    return docs


def calculate_cosine_similarity(text1, text2):
    """ 余弦相似度计算 """
    vectorizer = CountVectorizer(analyzer='word', token_pattern=u"(?u)\\b\\w+\\b")
    corpus = [text1, text2]
    vectors = vectorizer.fit_transform(corpus)
    similarity = cosine_similarity(vectors)
    return similarity[0][1]


def is_duplicate(docx, docy):
    """ 通过余弦相似度判断提取的特征值是否相似，相似度大于0.95则认为重复 """
    content_x = docx['extractContent']
    content_y = docy['extractContent']
    cos_sim = calculate_cosine_similarity(content_x, content_y)
    return cos_sim > SIMILARITY_THRESHOLD


def dup_process(doc_i, doc_j):
    """
    去重处理：
    1.按照来源优先级选取基数，数据来源一致的，取更新时间最新的一条
    2.字段补充
    :param doc_i:
    :param doc_j:
    :return:
    """

    def _supplement(x, y):
        """ 字段补全 """
        # 1.缺失字段补全
        for field in OPEN_COURT_SUPPLEMENT_FIELDS:
            if not x.get(field) and y.get(field):
                x[field] = y[field]
        # 2.取去重后nameId更全的案件相关人员字段
        x_name_ids, y_name_ids = set(), set()
        for field in RELATION_MEMBER_FIELDS:
            x_name_ids = x_name_ids.union({i.get('nameId') for i in x.get(field, []) if i.get('nameId')})
            y_name_ids = y_name_ids.union({i.get('nameId') for i in y.get(field, []) if i.get('nameId')})
        if len(x_name_ids) <= 5 and len(y_name_ids) <= 5:
            is_shorter = len(x_name_ids) < len(y_name_ids)
            for field in RELATION_MEMBER_FIELDS:
                if y.get(field) and (is_shorter or not x.get(field)):
                    x[field] = y[field]

    _id_i, _id_j = doc_i['_id'], doc_j['_id']
    source_i, source_j = doc_i['caSource'], doc_j['caSource']
    idx_i, idx_j = OPEN_CA_SOURCE_PRIORITY_LIST.index(source_i), OPEN_CA_SOURCE_PRIORITY_LIST.index(source_j)
    if idx_i < idx_j:
        _id = _id_j
        _supplement(doc_i, doc_j)
    elif idx_i > idx_j:
        _id = _id_i
        _supplement(doc_j, doc_i)
    else:
        update_time_i = doc_i['update_time']
        update_time_j = doc_j['update_time']
        if update_time_i > update_time_j:
            _id = _id_j
            _supplement(doc_i, doc_j)
        else:
            _id = _id_i
            _supplement(doc_j, doc_i)
    return _id


def is_duplicate_2(docx, docy):
    """ 判断重复规则2 """

    def _ca_date_condition(flag=False):
        if flag:
            return not (ca_date_x and ca_date_y)
        return ca_date_x and ca_date_y and ca_date_x == ca_date_y

    def _name_ids_condition():
        return name_ids_x and name_ids_y and \
            (len(name_ids_x - name_ids_y) == 0 or len(name_ids_y - name_ids_x) == 0)

    def _unit_condition():
        if not ca_publish_unit_x or not ca_publish_unit_y:
            return False
        unit_equal = ca_publish_unit_x == ca_publish_unit_x
        if unit_equal:
            return True
        if len(ca_publish_unit_x) >= 6 and len(ca_publish_unit_y) >= 6 and \
                ca_publish_unit_x not in COURT_BLACK_LIST and ca_publish_unit_y not in COURT_BLACK_LIST:
            return re.search(ca_publish_unit_x, ca_publish_unit_y) or \
                re.search(ca_publish_unit_y, ca_publish_unit_x)
        return False

    ca_date_x = docx.get('clean_date')
    ca_date_y = docy.get('clean_date')
    ca_publish_unit_x = docx.get('clean_publish_unit')
    ca_publish_unit_y = docy.get('clean_publish_unit')
    name_ids_x = {
        each['nameId'] for each in docx.get(OdsCourtAnnouncement.Field.caLitigantList, []) +
                                   docx.get(OdsCourtAnnouncement.Field.caPlaintiffList, []) +
                                   docx.get(OdsCourtAnnouncement.Field.caDefendant, []) +
                                   docx.get(OdsCourtAnnouncement.Field.caThirdList, [])
        if each.get('nameId')
    }
    name_ids_y = {
        each['nameId'] for each in docy.get(OdsCourtAnnouncement.Field.caLitigantList, []) +
                                   docy.get(OdsCourtAnnouncement.Field.caPlaintiffList, []) +
                                   docy.get(OdsCourtAnnouncement.Field.caDefendant, []) +
                                   docy.get(OdsCourtAnnouncement.Field.caThirdList, [])
        if each.get('nameId')
    }
    if _ca_date_condition() and (_name_ids_condition() or _unit_condition()):
        return True
    return _ca_date_condition(True) and _unit_condition() and _name_ids_condition()


def third_round_check(doc):
    """ 开庭时间和开庭法院非空检查 """
    return doc.get('clean_date') and doc.get('clean_publish_unit')


def map_third_round_dup_key(doc):
    clean_date = doc['clean_date']
    ca_publish_unit = doc['clean_publish_unit']
    return (clean_date, ca_publish_unit), doc


def is_duplicate_3(docx, docy):
    """ 判断重复规则3 """

    def _names_condition():
        return names_x and names_y and \
            (len(names_x - names_y) == 0 or len(names_y - names_x) == 0)

    def _case_number_condition():
        return not case_number_x and not case_number_y or (bool(case_number_x) ^ bool(case_number_y))

    names_x = {
        each['name'] for each in docx.get(OdsCourtAnnouncement.Field.caLitigantList, []) +
                                 docx.get(OdsCourtAnnouncement.Field.caPlaintiffList, []) +
                                 docx.get(OdsCourtAnnouncement.Field.caDefendant, []) +
                                 docx.get(OdsCourtAnnouncement.Field.caThirdList, [])
        if each.get('name') and not re.search(r'[*某]', each['name'])
    }
    names_y = {
        each['name'] for each in docy.get(OdsCourtAnnouncement.Field.caLitigantList, []) +
                                 docy.get(OdsCourtAnnouncement.Field.caPlaintiffList, []) +
                                 docy.get(OdsCourtAnnouncement.Field.caDefendant, []) +
                                 docy.get(OdsCourtAnnouncement.Field.caThirdList, [])
        if each.get('name') and not re.search(r'[*某]', each['name'])
    }
    case_number_x = docx.get('clean_case_number')
    case_number_y = docy.get('clean_case_number')
    if _case_number_condition() and _names_condition():
        return True
    return case_number_x and case_number_y and case_number_x == case_number_y


def fourth_round_check(doc):
    """ 开庭时间和人员nameId非空检查 """
    ca_related_list = doc.get(OdsCourtAnnouncement.Field.caLitigantList, []) + \
                      doc.get(OdsCourtAnnouncement.Field.caPlaintiffList, []) + \
                      doc.get(OdsCourtAnnouncement.Field.caDefendant, []) + \
                      doc.get(OdsCourtAnnouncement.Field.caThirdList, [])
    if ca_related_list:
        name_ids = [x.get('nameId') for x in ca_related_list]
        if any(name_ids):
            return doc.get('clean_date')
    return False


def map_fourth_round_dup_key(doc):
    clean_date = doc.get('clean_date')
    ca_related_list = doc.get(OdsCourtAnnouncement.Field.caLitigantList, []) + \
                      doc.get(OdsCourtAnnouncement.Field.caPlaintiffList, []) + \
                      doc.get(OdsCourtAnnouncement.Field.caDefendant, []) + \
                      doc.get(OdsCourtAnnouncement.Field.caThirdList, [])
    name_ids = list({x['nameId'] for x in ca_related_list if x.get('nameId')})
    name_ids.sort()
    return (clean_date, '_'.join(name_ids)), doc


def is_duplicate_4(docx, docy):
    """ 判断重复规则4 """

    def _case_number_condition():
        return not case_number_x and not case_number_y or (bool(case_number_x) ^ bool(case_number_y))

    def _unit_condition():
        if not ca_publish_unit_x or not ca_publish_unit_y:
            return False
        unit_equal = ca_publish_unit_x == ca_publish_unit_y
        if unit_equal:
            return True
        if len(ca_publish_unit_x) >= 6 and len(ca_publish_unit_y) >= 6 and \
                ca_publish_unit_x not in COURT_BLACK_LIST and ca_publish_unit_y not in COURT_BLACK_LIST:
            return re.search(ca_publish_unit_x, ca_publish_unit_y) or \
                re.search(ca_publish_unit_y, ca_publish_unit_x)
        return False

    case_number_x = docx.get('clean_case_number')
    case_number_y = docy.get('clean_case_number')
    if case_number_x and case_number_y and case_number_x == case_number_y:
        return True
    ca_publish_unit_x = docx.get('clean_publish_unit')
    ca_publish_unit_y = docy.get('clean_publish_unit')
    if _case_number_condition() and _unit_condition():
        return True
    return False


def case_member_process(doc):
    """
    1. 去掉临时字段
    2. 公告的案件人员融合处理
    :param doc:
    :return:
    """
    for field in ['extractContent', 'clean_publish_unit', 'clean_case_number', 'clean_date']:
        doc.pop(field, None)

    # 关联方
    ca_relation_list = doc.get(DwmEnterpriseCourtAnnouncement.Field.caRelationList, [])
    ca_relation_names = {each['name'] for each in ca_relation_list if each.get('name')}
    # 起诉人
    ca_plaintiff_list = doc.get(OdsCourtAnnouncement.Field.caPlaintiffList, [])
    ca_plaintiff_names = {each['name'] for each in ca_plaintiff_list if each.get('name')}
    # 被告人（旧坑：被告字段名被修改了）
    ca_litigant_list = doc.get(OdsCourtAnnouncement.Field.caLitigantList, [])
    ca_litigant_names = {each['name'] for each in ca_litigant_list if each.get('name')}
    # 第三人
    ca_third_list = doc.get(OdsCourtAnnouncement.Field.caThirdList, [])
    ca_third_names = {each['name'] for each in ca_third_list if each.get('name')}

    remove_members = set()
    # 起诉人列表清洗
    for each in ca_plaintiff_list[::-1]:
        name = each.get('name')
        if name in ca_litigant_names or name in ca_third_names:
            ca_plaintiff_list.remove(each)
            if name not in remove_members:
                remove_members.add(name)
    # 被告人列表清洗
    for each in ca_litigant_list[::-1]:
        name = each.get('name')
        if name in ca_plaintiff_names or name in ca_third_names:
            ca_litigant_list.remove(each)
            if name not in remove_members:
                remove_members.add(name)
    # 第三人列表清洗
    for each in ca_third_list[::-1]:
        name = each.get('name')
        if name in ca_plaintiff_names or name in ca_litigant_names:
            ca_third_list.remove(each)
            if name not in remove_members:
                remove_members.add(name)
    # 其他当事人列表
    ca_parties_names = ca_relation_names - ca_plaintiff_names - ca_litigant_names - ca_third_names
    if remove_members:
        ca_parties_names = ca_parties_names.union(remove_members)
    ca_parties_list = []
    for each in ca_relation_list:
        name = each.get('name')
        if name in ca_parties_names:
            ca_parties_list.append(each)
    if ca_parties_list:
        doc[DwmEnterpriseCourtAnnouncement.Field.caPartiesList] = ca_parties_list
    return doc
