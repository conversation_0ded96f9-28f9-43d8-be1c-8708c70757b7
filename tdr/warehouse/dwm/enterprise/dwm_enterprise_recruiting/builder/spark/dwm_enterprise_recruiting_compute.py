#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    ~~~~~~~~~~~~~~~~~~~~~~~

    Description of this file

    :author: l<PERSON><PERSON><PERSON>
    :copyright: (c) dwm_enterprise_recruiting_compute.py, Tungee
    :date created: 2022/12/20
    :python version: 3.6

"""
import sys

from tdr.common.constant.module import ModuleNameConstant
from tdr.common.utils.spark.spark_utils import *
from tdr.warehouse.dwm.enterprise.dwm_enterprise_recruiting.meta.dwm_enterprise_recruiting import DwmEnterpriseRecruiting
from tdr.warehouse.dwm.enterprise.dwm_enterprise_recruiting.utils.dwm_enterprise_recruiting_util import *
from tdr.warehouse.utils.spark import get_rdd_from_file
# 切分文件用于入es
RECRUITING_NEED_FIELD_LIST = [
    OdsRecruiting.Field.id,
    OdsRecruiting.Field.nameId,
    OdsRecruiting.Field.workingAddress,
    OdsRecruiting.Field.recruitingExists,
    OdsRecruiting.Field.recruitingDistrict,
    OdsRecruiting.Field.recruitingPublishedTime,
    OdsRecruiting.Field.recruitingType,
    OdsRecruiting.Field.recruitingName,
    OdsRecruiting.Field.recruitingDesc,
    OdsRecruiting.Field.recruitingSource,
    OdsRecruiting.Field.recruitingCount,
    OdsRecruiting.Field.benefitList,
]
JOB_NAME = 'DW_{}_{}'.format(ModuleNameConstant.enterprise, DwmEnterpriseRecruiting.NAME)


def main(*args):
    """ 企业经营风险数据计算入口
    """
    [
        ods_enterprise_recruiting_input,
        dwm_enterprise_recruiting_mongo_output,
        n_partition
    ] = args

    spark = SparkSession.builder.appName(JOB_NAME).getOrCreate()
    sc = spark.sparkContext
    n_partition = int(n_partition)

    ods_enterprise_recruiting_rdd = get_rdd_from_file(sc, ods_enterprise_recruiting_input)

    valid_recruiting_rdd = ods_enterprise_recruiting_rdd.filter(mongo_filter_recruiting)

    mongo_out_put_rdd = valid_recruiting_rdd.filter(
        lambda doc: doc.get(OdsRecruiting.Field.recruitingExists, True) is not False
    ).map(
        lambda doc: {field: doc[field] for field in RECRUITING_NEED_FIELD_LIST if field in doc}
    )
    persist_rdd(mongo_out_put_rdd, dwm_enterprise_recruiting_mongo_output, numPartitions=n_partition)


if __name__ == '__main__':
    main(*sys.argv[1:])
