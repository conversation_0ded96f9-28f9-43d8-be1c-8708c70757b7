#!/usr/bin/env python
# -*- coding: utf-8 -*-

from tdr.common.utils.time_helper import convert_arbitrary_date_format
from tdr.warehouse.ods.ods_recruiting.meta.ods_recruiting import OdsRecruiting


def mongo_filter_recruiting(recruiting_dict):
    """
    1. 过滤没有招聘时间的数据

    :param recruiting_dict:
    :return:
    """
    if OdsRecruiting.Field.recruitingPublishedTime not in recruiting_dict \
            or OdsRecruiting.Field.nameId not in recruiting_dict \
            or OdsRecruiting.Field.recruitingSource not in recruiting_dict \
            or OdsRecruiting.Field.recruitingName not in recruiting_dict:
        return False
    # 招聘时间不存在
    if convert_arbitrary_date_format(
            recruiting_dict[OdsRecruiting.Field.recruitingPublishedTime]) is None:
        return False
    return True
