#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    ~~~~~~~~~~~~~~~~~~~~~~~

    Description of this file

    :author: l<PERSON><PERSON><PERSON>
    :copyright: (c) dwm_enterprise_recruiting.py, Tungee
    :date created: 2022/12/20
    :python version: 3.6

"""
from tdr.common.constant.module import ModuleNameConstant
from tdr.warehouse.ods.ods_recruiting.meta.ods_recruiting import OdsRecruiting


class DwmEnterpriseRecruiting(object):
    NAME = 'dwm_enterprise_recruiting'

    MODULE_NAME = ModuleNameConstant.enterprise

    class Field:
        id = 'id'
        nameId = 'nameId'
        workingAddress = 'workingAddress'
        recruitingExists = 'recruitingExists'
        recruitingDistrict = 'recruitingDistrict'
        recruitingPublishedTime = 'recruitingPublishedTime'
        recruitingType = 'recruitingType'
        recruitingName = 'recruitingName'
        recruitingDesc = 'recruitingDesc'
        recruitingSource = 'recruitingSource'
        recruitingCount = 'recruitingCount'
        benefitList = 'benefitList'

        class RecruitingDistrictField:
            city = 'city'
            district = 'district'
            name = 'name'
            province = 'province'
            subdistrict = 'subdistrict'
            value = 'value'

        class WorkingAddressField:
            city = 'city'
            district = 'district'
            name = 'name'
            province = 'province'
            subdistrict = 'subdistrict'
            value = 'value'

    # 输入
    INPUTS = [OdsRecruiting]

    # Spark Schema
    SPARK_SCHEMA = {
        "fields": [
            {
                "metadata": {

                },
                "name": "_id",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "benefitList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "nameId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "recruitingCount",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "max",
                            "nullable": True,
                            "type": "long"
                        },
                        {
                            "metadata": {

                            },
                            "name": "min",
                            "nullable": True,
                            "type": "long"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "recruitingDesc",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "recruitingDistrict",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "city",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "district",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "name",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "province",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "subdistrict",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "value",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "recruitingExists",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {

                },
                "name": "recruitingName",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "recruitingPublishedTime",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "recruitingSource",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "recruitingType",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "workingAddress",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "city",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "district",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "name",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "province",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "subdistrict",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "value",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            }
        ],
        "type": "struct"
    }
