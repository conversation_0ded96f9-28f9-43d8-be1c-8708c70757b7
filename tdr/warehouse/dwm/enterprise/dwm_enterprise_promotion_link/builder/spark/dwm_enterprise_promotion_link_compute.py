# -*- coding: utf-8 -*-
"""
    dwm_enterprise_promotion_link_compute
    ~~~~~~~
    
    Description
    
    :author: <PERSON>
    :copyright: (c) 2022, Tungee
    :date created: 2022-12-08
    :python version: 
"""
from tdr.common.constant.module import ModuleNameConstant
from tdr.common.utils.md5_util import str_md5
import sys

from pyspark import RDD
from pyspark.sql import SparkSession

from tdr.common.utils.spark.spark_utils import persist_rdd
from tdr.warehouse.dwm.enterprise.dwm_enterprise_promotion_link.meta.dwm_enterprise_promotion_link import \
    DwmEnterprisePromotionLink as DwmEPL, DwmEnterprisePromotionLink
from tdr.warehouse.ods.ods_promotion.meta.ods_promotion import OdsPromotion
from tdr.warehouse.utils.spark import get_rdd_from_file
JOB_NAME = 'DW_{}_{}'.format(ModuleNameConstant.enterprise, DwmEnterprisePromotionLink.NAME)


def promotion_filter(promotion_dict):
    """
    过滤数据：缺少企业映射、缺少推广源、缺少推广关键词
    """
    for k in ['nameId', 'prmtLink']:
        if k not in promotion_dict:
            return False
    return True


def map_promotion_link(promotion_dict):
    name_id = promotion_dict[OdsPromotion.Field.nameId]
    key = promotion_dict[OdsPromotion.Field.prmtKey]
    time = promotion_dict.get(OdsPromotion.Field.prmtTime)
    link = promotion_dict[OdsPromotion.Field.prmtLink]

    name_link_data = {
        DwmEPL.Field.prmtKeys: [
            {
                OdsPromotion.Field.prmtTime: time,
                OdsPromotion.Field.prmtKey: key
            }
        ],
        DwmEPL.Field.lastPrmtTime: time,
    }

    return name_id + '_' + link, name_link_data


def reduce_promotion_link(x_dict, y_dict):
    # 合并推广关键词
    x_dict[DwmEPL.Field.prmtKeys] = x_dict[DwmEPL.Field.prmtKeys] + y_dict[DwmEPL.Field.prmtKeys]

    # 推广时间取更新的
    if x_dict[DwmEPL.Field.lastPrmtTime] < y_dict[DwmEPL.Field.lastPrmtTime]:
        x_dict[DwmEPL.Field.lastPrmtTime] = y_dict[DwmEPL.Field.lastPrmtTime]

    return x_dict


def map_format_promotion(data):
    key, promotion_link_dict = data
    words = key.split('_')
    name_id, link = words[0], '_'.join(words[1:])
    promotion_link_dict[DwmEPL.Field.id] = str_md5(key, digit=16)
    promotion_link_dict[DwmEPL.Field.nameId] = name_id
    promotion_link_dict[DwmEPL.Field.prmtLink] = link

    # 推广关键词按推广时间排序
    prmt_keys = promotion_link_dict[DwmEPL.Field.prmtKeys]
    prmt_keys = sorted(prmt_keys, key=lambda k: k[OdsPromotion.Field.prmtTime], reverse=True)

    # 去重
    unique_keys = list()
    key_set = set()
    for key_doc in prmt_keys:
        key = key_doc[OdsPromotion.Field.prmtKey]
        if key not in key_set:
            unique_keys.append(key)
            key_set.add(key)

    # 只需要前三个
    promotion_link_dict[DwmEPL.Field.prmtKeys] = unique_keys[0: 3]
    promotion_link_dict[DwmEPL.Field.prmtKeysNumber] = len(unique_keys)
    return promotion_link_dict


def run(*args):
    spark = SparkSession.builder.appName(JOB_NAME).getOrCreate()
    sc = spark.sparkContext
    ods_promotion_input, dwm_enterprise_promotion_link_output, n_partition = args
    n_partition = int(n_partition)
    ods_promotion_rdd: RDD = get_rdd_from_file(sc, ods_promotion_input)
    dwm_enterprise_promotion_link_rdd = ods_promotion_rdd.filter(
        promotion_filter
    ).map(
        map_promotion_link
    ).reduceByKey(
        reduce_promotion_link
    ).map(
        map_format_promotion
    )
    persist_rdd(dwm_enterprise_promotion_link_rdd, dwm_enterprise_promotion_link_output, numPartitions=n_partition)


if __name__ == '__main__':
    run(*sys.argv[1:])