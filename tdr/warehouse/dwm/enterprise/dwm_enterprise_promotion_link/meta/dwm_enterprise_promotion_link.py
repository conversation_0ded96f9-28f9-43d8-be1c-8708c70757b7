# -*- coding: utf-8 -*-
"""
    dwm_enterprise_promotion_link
    ~~~~~~~
    
    Description
    
    :author: <PERSON>
    :copyright: (c) 2022, Tungee
    :date created: 2022-12-08
    :python version: 
"""
from tdr.common.constant.common import FormatType
from tdr.warehouse.ods.ods_promotion.meta.ods_promotion import OdsPromotion


class DwmEnterprisePromotionLink(object):
    NAME = 'dwm_enterprise_promotion_link'

    FORMAT_TYPE = FormatType.json

    class Field:
        id = '_id'
        nameId = 'nameId'
        lastPrmtTime = 'lastPrmtTime'
        prmtKeys = 'prmtKeys'
        prmtKeysNumber = 'prmtKeysNumber'
        prmtLink = 'prmtLink'
        type = 'type'

    # 输入
    INPUTS = [OdsPromotion]

    # Spark Schema
    SPARK_SCHEMA = {
        "fields": [
            {
                "metadata": {

                },
                "name": "_id",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "lastPrmtTime",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "nameId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "prmtKeys",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "prmtKeysNumber",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {

                },
                "name": "prmtLink",
                "nullable": True,
                "type": "string"
            }
        ],
        "type": "struct"
    }
