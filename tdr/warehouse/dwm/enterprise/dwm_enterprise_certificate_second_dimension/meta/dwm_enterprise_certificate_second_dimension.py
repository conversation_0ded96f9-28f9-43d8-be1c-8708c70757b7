#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    ~~~~~~~~~~~~~~~~~~~~~~~

    Description of this file

    :author: l<PERSON><PERSON><PERSON>
    :copyright: (c) dwm_enterprise_certificate_second_dimension.py, Tungee
    :date created: 2022/12/19
    :python version: 3.6

"""
from tdr.common.constant.common import FormatType
from tdr.warehouse.dwm.enterprise.dwm_enterprise_certificate_cal.meta.dwm_enterprise_certificate_cal import \
    DwmEnterpriseCertificateCal
from tdr.warehouse.ods.ods_enterprise_annual_qualification.meta.ods_enterprise_annual_qualification import \
    OdsEnterpriseAnnualQualification


class DwmEnterpriseCertificateSecondDimension(object):
    NAME = 'dwm_enterprise_certificate_second_dimension'

    FORMAT_TYPE = FormatType.json

    class Field:
        _id = '_id'
        nameId = 'nameId'
        hasCertificate = 'hasCertificate'
        is_technologic = 'isTechnologic'
        last_tech_cert_publish_time = 'lastTechCertPublishTime'
        last_tech_cert_expire_time = 'lastTechCertExpireTime'
        last_tech_cert_authority = 'lastTechCertAuthority'
        cert_type_list = 'certTypeList'
        cert_name_list = 'certNameList'
        to_be_renewed_cert_type_list = 'toBeRenewedCertTypeList'  # 待续办证书类别
        has_to_be_renewed = 'hasToBoRenewed'  # 是否有待续办
        cert_status_list = 'certStatusList'  # 证书状态
        cert_type_stats = 'certTypeStats'  # 证书类别统计
        first_time_of_hi_tech_certification = 'firstTimeOfHiTechCertification'
        expiration_time_of_hi_tech_certification = 'expirationTimeOfHiTechCertification'  # 高新企业认证到期时间
        tech_certification_date = 'techCertificationDate'  # 高新技术企业认证日期
        cert_type_list_last_three_month = 'certTypeListLastThreeMonth'
        cert_type_list_last_six_month = 'certTypeListLastSixMonth'
        cert_expire_time_list = 'certExpireTimeList'
        certFirstPublishTime = 'certFirstPublishTime'
        certLastPublishTime = 'certLastPublishTime'
        certAuthorityList = 'certAuthorityList'
        is_high_tech_enterprise = 'isHighTechEnterprise'
        isSpecializedAndNew = 'isSpecializedAndNew'  # 专精特新
        isSpecializedAndNewGiant = 'isSpecializedAndNewGiant'  # 专精特新小巨人
        certNumber = 'certNumber'
        is_history_technologic = 'isHistoryTechnologic'
        certNumberThisYear = 'certNumberThisYear'
        cert_standard_list = 'certStandardList'
        cert_id_list = 'certIdList'

        tab_stats = 'tabStats'

    class TabStatsType:
        certificate = 'certificate'
        operating_info_certificate = 'operating_info::certificate'

    # 输入
    INPUTS = [OdsEnterpriseAnnualQualification, DwmEnterpriseCertificateCal]

    SPARK_SCHEMA = {
        "fields": [
            {
                "metadata": {

                },
                "name": "_id",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "nameId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "hasCertificate",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {

                },
                "name": "isTechnologic",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {

                },
                "name": "lastTechCertPublishTime",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "lastTechCertExpireTime",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "lastTechCertAuthority",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certTypeList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "certNameList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "toBeRenewedCertTypeList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "hasToBoRenewed",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {

                },
                "name": "certStatusList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "certTypeStats",
                "nullable": True,
                "type": {
                    "keyType": "string",
                    "type": "map",
                    "valueContainsNull": True,
                    "valueType": "long"
                }
            },
            {
                "metadata": {

                },
                "name": "firstTimeOfHiTechCertification",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "expirationTimeOfHiTechCertification",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "techCertificationDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certTypeListLastThreeMonth",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "certTypeListLastSixMonth",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "certExpireTimeList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "certFirstPublishTime",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certLastPublishTime",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certAuthorityList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "isHighTechEnterprise",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {

                },
                "name": "certNumber",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {

                },
                "name": "tabStats",
                "nullable": True,
                "type": {
                    "keyType": "string",
                    "type": "map",
                    "valueContainsNull": True,
                    "valueType": "long"
                }
            },
            {
                "metadata": {

                },
                "name": "isHistoryTechnologic",
                "nullable": True,
                "type": "integer"
            },
            {
                "metadata": {

                },
                "name": "certNumberThisYear",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {

                },
                "name": "certStandardList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "certIdList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "isSpecializedAndNew",
                "nullable": True,
                "type": "integer"
            },
            {
                "metadata": {

                },
                "name": "isSpecializedAndNewGiant",
                "nullable": True,
                "type": "integer"
            },
        ],
        "type": "struct"
    }