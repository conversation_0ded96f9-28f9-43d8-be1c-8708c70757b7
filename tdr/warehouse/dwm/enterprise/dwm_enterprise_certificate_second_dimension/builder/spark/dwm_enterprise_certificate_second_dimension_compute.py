#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    ~~~~~~~~~~~~~~~~~~~~~~~

    Description of this file

    :author: <PERSON><PERSON><PERSON><PERSON>
    :copyright: (c) dwm_enterprise_certificate_compute.py, Tungee
    :date created: 2022/12/19
    :python version: 3.6

"""
import sys

from tdr.common.constant.module import ModuleNameConstant
from tdr.common.utils.spark.spark_utils import *

from tdr.warehouse.dwm.enterprise.dwm_enterprise_certificate_second_dimension.utils.dwm_enterprise_certificate_second_dimension_util import *
from tdr.warehouse.dwm.enterprise.dwm_enterprise_certificate_second_dimension.meta.dwm_enterprise_certificate_second_dimension import \
    DwmEnterpriseCertificateSecondDimension
from tdr.warehouse.ods.ods_enterprise_annual_qualification.meta.ods_enterprise_annual_qualification import \
    OdsEnterpriseAnnualQualification

JOB_NAME = 'DW_{}_{}'.format(ModuleNameConstant.enterprise, DwmEnterpriseCertificateSecondDimension.NAME)


def main(*args):
    [
        ods_enterprise_annual_qualification_input,
        dwm_certificate_mongo_input,
        dwm_certificate_merge_output,
        n_partition
    ] = args

    spark = SparkSession.builder.appName(JOB_NAME).getOrCreate()
    sc = spark.sparkContext
    n_partition = int(n_partition)

    dwm_certificate_mongo_rdd = get_rdd_from_file(sc, dwm_certificate_mongo_input)
    ods_enterprise_annual_qualification_rdd = get_rdd_from_file(sc, ods_enterprise_annual_qualification_input)

    enterprise_annual_qualification_rdd = ods_enterprise_annual_qualification_rdd.filter(
        enterprise_annual_qualification_filter
    ).map(
        lambda doc: ((doc[OdsEnterpriseAnnualQualification.Field.eaqSource],
                      doc[OdsEnterpriseAnnualQualification.Field.eaqEnterpriseName],
                      doc[OdsEnterpriseAnnualQualification.Field.eaqType],
                      doc[OdsEnterpriseAnnualQualification.Field.eaqYear]), doc)
    ).reduceByKey(
        lambda x, y: x or y  # 去重
    ).values().map(
        map_enterprise_annual_qualification
    ).filter(lambda x: x)

    # 部分维度 - 根据企业id进行合并统计维度
    tmp_rdd = dwm_certificate_mongo_rdd.map(
        lambda doc: (doc[OdsEnterpriseAnnualQualification.Field.nameId], [doc])
    ).reduceByKey(
        lambda x, y: x + y
    ).map(
        map_enterprise_certificate_list_process
    ).fullOuterJoin(
        enterprise_annual_qualification_rdd
    ).map(
        merge_tech_enterprise
    ).filter(lambda x: x)
    persist_rdd(tmp_rdd, dwm_certificate_merge_output, numPartitions=n_partition)


if __name__ == '__main__':
    main(*sys.argv[1:])
