#!/usr/bin/env python
# -*- coding: utf-8 -*-
from collections import defaultdict
from datetime import timedelta

from tdr.common.utils.time_helper import convert_arbitrary_date_format
from tdr.warehouse.dwm.enterprise.dwm_enterprise_certificate_cal.meta.dwm_enterprise_certificate_cal import \
    DwmEnterpriseCertificateCal
from tdr.warehouse.dwm.enterprise.dwm_enterprise_certificate_cal.utils.constant import *
from tdr.warehouse.dwm.enterprise.dwm_enterprise_certificate_second_dimension.meta.dwm_enterprise_certificate_second_dimension import \
    DwmEnterpriseCertificateSecondDimension
from tdr.warehouse.ods.ods_certificate.meta.ods_certificate import OdsCertificate
from tdr.warehouse.ods.ods_enterprise_annual_qualification.meta.ods_enterprise_annual_qualification import \
    OdsEnterpriseAnnualQualification

EAQ_TYPE_WHITELIST = {
    '高新技术企业备案公示企业', '专精特新小巨人', '专精特新中小企业',
}
NOW_STR = datetime.today().strftime('%Y-%m-%d')


def get_cert_authority(info_list, default):
    if not info_list:
        return default
    for each in info_list:
        info_k = each.get('key')
        info_v = each.get('value')
        if info_k in ['发证机构名称', '机构名称']:
            return info_v
    return default


def map_enterprise_certificate_list_process(data):
    """
    企业证书处理
    """
    now = datetime.strptime(datetime.now().strftime('%Y-%m-%d'), '%Y-%m-%d')
    key, cert_list = data[0], data[1]
    doc = {DwmEnterpriseCertificateSecondDimension.Field._id: key, DwmEnterpriseCertificateSecondDimension.Field.hasCertificate: 1}

    cert_type_set, cert_name_set = set(), set()
    to_be_renewed_cert_type_set = set()  # 待续办证书类别
    cert_status_set, cert_standard_set, cert_id_set = set(), set(), set()
    cert_type_stats = defaultdict(int)
    last_tech_expire_time, last_tech_expire_cert = None, {}
    for cert_doc in cert_list:
        if DwmEnterpriseCertificateCal.Field.cert_type in cert_doc:
            cert_type_set.add(cert_doc[DwmEnterpriseCertificateCal.Field.cert_type])
            cert_type_stats[cert_doc[DwmEnterpriseCertificateCal.Field.cert_type]] += 1
        if cert_doc.get(DwmEnterpriseCertificateCal.Field.cert_name_list):
            cert_name_set = cert_name_set.union(set(cert_doc[DwmEnterpriseCertificateCal.Field.cert_name_list]))
        if cert_doc.get(DwmEnterpriseCertificateCal.Field.to_be_renewed) and cert_doc.get(DwmEnterpriseCertificateCal.Field.cert_type):
            to_be_renewed_cert_type_set.add(cert_doc[DwmEnterpriseCertificateCal.Field.cert_type])
        if cert_doc.get(DwmEnterpriseCertificateCal.Field.cal_cert_status):
            cert_status_set.add(cert_doc[DwmEnterpriseCertificateCal.Field.cal_cert_status])
        if cert_doc.get(DwmEnterpriseCertificateCal.Field.is_history_technologic):
            doc[DwmEnterpriseCertificateSecondDimension.Field.is_history_technologic] = 1
        if cert_doc.get(DwmEnterpriseCertificateCal.Field.certId):
            cert_id_set.add(cert_doc[DwmEnterpriseCertificateCal.Field.certId])
        if cert_doc.get(DwmEnterpriseCertificateCal.Field.cert_standard):
            cert_standard_set.add(cert_doc[DwmEnterpriseCertificateCal.Field.cert_standard])
        if (cert_doc.get(DwmEnterpriseCertificateCal.Field.is_technologic) or cert_doc.get(DwmEnterpriseCertificateCal.Field.is_history_technologic)) and cert_doc.get(DwmEnterpriseCertificateCal.Field.cert_expire_time):
            if last_tech_expire_time is None or cert_doc.get(DwmEnterpriseCertificateCal.Field.cert_expire_time) > last_tech_expire_time:
                last_tech_expire_time = cert_doc.get(DwmEnterpriseCertificateCal.Field.cert_expire_time)
                last_tech_expire_cert = cert_doc
    if last_tech_expire_cert:
        if last_tech_expire_cert.get(DwmEnterpriseCertificateCal.Field.is_history_technologic, 0) == 0:
            doc[DwmEnterpriseCertificateSecondDimension.Field.is_technologic] = 1
            doc[DwmEnterpriseCertificateSecondDimension.Field.is_history_technologic] = 0
        else:
            doc[DwmEnterpriseCertificateSecondDimension.Field.is_technologic] = 0
            doc[DwmEnterpriseCertificateSecondDimension.Field.is_history_technologic] = 1
        doc[DwmEnterpriseCertificateSecondDimension.Field.last_tech_cert_publish_time] = last_tech_expire_cert.get(DwmEnterpriseCertificateCal.Field.cert_publish_time)
        doc[DwmEnterpriseCertificateSecondDimension.Field.last_tech_cert_expire_time] = last_tech_expire_cert.get(DwmEnterpriseCertificateCal.Field.cert_expire_time)
        if last_tech_expire_cert.get(DwmEnterpriseCertificateCal.Field.cert_authority):
            doc[DwmEnterpriseCertificateSecondDimension.Field.last_tech_cert_authority] = last_tech_expire_cert.get(DwmEnterpriseCertificateCal.Field.cert_authority)
        elif last_tech_expire_cert.get(DwmEnterpriseCertificateCal.Field.certBasicInfoList):
            cert_basic_info_list = last_tech_expire_cert.get(DwmEnterpriseCertificateCal.Field.certBasicInfoList)
            if cert_basic_info_list and isinstance(cert_basic_info_list, list) and len(list(cert_basic_info_list)) > 0:
                for cert_basic_info in cert_basic_info_list:
                    if cert_basic_info.get('key') and cert_basic_info.get('key') == '入库登记机关':
                        doc[DwmEnterpriseCertificateSecondDimension.Field.last_tech_cert_authority] = cert_basic_info['value']
                        break

    # 证书资质类别
    if len(cert_type_set) > 0:
        doc[DwmEnterpriseCertificateSecondDimension.Field.cert_type_list] = list(cert_type_set)
    # 证书名
    if len(cert_name_set) > 0:
        doc[DwmEnterpriseCertificateSecondDimension.Field.cert_name_list] = list(cert_name_set)
    # 待续办证书类别
    if len(to_be_renewed_cert_type_set):
        doc[DwmEnterpriseCertificateSecondDimension.Field.to_be_renewed_cert_type_list] = list(to_be_renewed_cert_type_set)
        doc[DwmEnterpriseCertificateSecondDimension.Field.has_to_be_renewed] = 1
    # 证书状态
    if len(cert_status_set):
        doc[DwmEnterpriseCertificateSecondDimension.Field.cert_status_list] = list(cert_status_set)
    # 证书类型统计
    if cert_type_stats:
        doc[DwmEnterpriseCertificateSecondDimension.Field.cert_type_stats] = cert_type_stats
    # 认证标准和技术要求
    if cert_standard_set:
        doc[DwmEnterpriseCertificateSecondDimension.Field.cert_standard_list] = list(cert_standard_set)[:1000]
    # 证书编号
    if cert_id_set:
        doc[DwmEnterpriseCertificateSecondDimension.Field.cert_id_list] = list(cert_id_set)[:1000]

    # 近一年获证数量
    # 初次获证日期, 最近获证日期
    cert_number_this_year = 0
    first_cert_date, last_cert_date = None, None
    is_high_tech_enterprise = False
    expire_time_list = set()
    stats = defaultdict(set)
    for cert_doc in cert_list:
        if DwmEnterpriseCertificateCal.Field.cert_publish_time in cert_doc:
            # 发证日期
            publish_time = cert_doc.get(DwmEnterpriseCertificateCal.Field.cert_publish_time)
            try:
                publish_datetime = convert_arbitrary_date_format(publish_time)
            except Exception:
                # 时间格式无法解析
                continue
            # 近一年获证数量
            if publish_datetime >= now - timedelta(days=365):
                cert_number_this_year += 1
            # 初次获证日期
            if first_cert_date is None:
                first_cert_date = publish_datetime
            else:
                if publish_datetime < first_cert_date:
                    first_cert_date = publish_datetime
            # 最近获证日期
            if last_cert_date is None:
                last_cert_date = publish_datetime
            else:
                if publish_datetime > last_cert_date:
                    last_cert_date = publish_datetime

        expire_time = cert_doc.get(DwmEnterpriseCertificateCal.Field.cert_expire_time) or ''
        try:
            expire_time = convert_arbitrary_date_format(expire_time)
        except Exception:
            pass

        # 获取高新技术企业证书的起始认证时间和到期认证时间
        if cert_doc[DwmEnterpriseCertificateCal.Field.certSource] == 'innocom' and cert_doc.get(DwmEnterpriseCertificateCal.Field.cert_type) == '高新技术企业':
            if first_cert_date:
                doc[DwmEnterpriseCertificateSecondDimension.Field.first_time_of_hi_tech_certification] = first_cert_date.strftime('%Y-%m-%d')
            if expire_time:
                doc[DwmEnterpriseCertificateSecondDimension.Field.expiration_time_of_hi_tech_certification] = expire_time.strftime('%Y-%m-%d')

        if expire_time:
            if cert_doc[DwmEnterpriseCertificateCal.Field.certSource] == 'innocom' and expire_time > now:
                is_high_tech_enterprise = True
                if cert_doc.get(DwmEnterpriseCertificateCal.Field.cert_publish_time):
                    doc[DwmEnterpriseCertificateSecondDimension.Field.tech_certification_date] = cert_doc[DwmEnterpriseCertificateCal.Field.cert_publish_time]

            time_string = expire_time.strftime('%Y-%m-%d')
            expire_time_list.add(time_string)

            if now <= expire_time < (now + timedelta(days=90 + 1)) and cert_doc.get(DwmEnterpriseCertificateCal.Field.cert_type):
                stats[DwmEnterpriseCertificateSecondDimension.Field.cert_type_list_last_three_month].add(
                    cert_doc[DwmEnterpriseCertificateCal.Field.cert_type])

            if now <= expire_time < (now + timedelta(days=180 + 1)) and cert_doc.get(DwmEnterpriseCertificateCal.Field.cert_type):
                stats[DwmEnterpriseCertificateSecondDimension.Field.cert_type_list_last_six_month].add(
                    cert_doc['certType'])

    if expire_time_list:
        doc[DwmEnterpriseCertificateSecondDimension.Field.cert_expire_time_list] = list(expire_time_list)

    for key, value in stats.items():
        if value:
            doc[key] = list(value)

    doc['certNumberThisYear'] = cert_number_this_year
    if first_cert_date is not None:
        doc[DwmEnterpriseCertificateSecondDimension.Field.certFirstPublishTime] = first_cert_date.strftime('%Y-%m-%d')
    if last_cert_date is not None:
        doc[DwmEnterpriseCertificateSecondDimension.Field.certLastPublishTime] = last_cert_date.strftime('%Y-%m-%d')

    # 证书数量
    doc[DwmEnterpriseCertificateSecondDimension.Field.certNumber] = len(cert_list)
    doc[DwmEnterpriseCertificateSecondDimension.Field.tab_stats] = {
        DwmEnterpriseCertificateSecondDimension.TabStatsType.certificate: len(cert_list),
        DwmEnterpriseCertificateSecondDimension.TabStatsType.operating_info_certificate: len(cert_list),
    }

    # 发证机关列表
    cert_authority_set = set()
    for cert_doc in cert_list:
        # 如果来源为cnca，则优先取certAuthorityInfoList，否则优先取certAuthority
        cert_authority_info_list = cert_doc.get(DwmEnterpriseCertificateCal.Field.certAuthorityInfoList, [])
        cert_authority = cert_doc.get(OdsCertificate.Field.certAuthority)
        if cert_doc.get(DwmEnterpriseCertificateCal.Field.certSource) == 'cnca' or not cert_authority:
            cert_authority = get_cert_authority(cert_authority_info_list, cert_authority)
        if cert_authority:
            cert_authority_set.add(cert_authority)
    if cert_authority_set:
        doc[DwmEnterpriseCertificateSecondDimension.Field.certAuthorityList] = list(cert_authority_set)

    if is_high_tech_enterprise:
        doc[DwmEnterpriseCertificateSecondDimension.Field.is_high_tech_enterprise] = 1
    return key, doc


def map_enterprise_annual_qualification(doc):
    if doc.get('eaqType') == '高新技术企业备案公示企业':
        eaqPublishDate = doc.get('eaqPublishDate')
        if eaqPublishDate:
            if not doc.get('eaqBeginDate'):
                doc['eaqBeginDate'] = eaqPublishDate
            if not doc.get('eaqEndDate'):
                try:
                    dt = convert_arbitrary_date_format(eaqPublishDate, ['%Y-%m-%d'])
                    assert dt
                except:
                    pass
                else:
                    eaqEndDate = datetime(year=dt.year + 3, month=dt.month, day=dt.day)
                    doc['eaqEndDate'] = eaqEndDate.strftime('%Y-%m-%d')
    if doc.get('eaqBeginDate', '') <= NOW_STR < doc.get('eaqEndDate', ''):
        return doc[OdsEnterpriseAnnualQualification.Field.nameId], {
            "eaqType": doc['eaqType'], 'eaqPublishDate': doc.get('eaqPublishDate')}


def merge_tech_enterprise(input_data):
    key, (doc, eaq_doc) = input_data
    if doc:
        if eaq_doc:
            eaqType = eaq_doc.get('eaqType')
            if eaqType == '高新技术企业备案公示企业':
                doc[DwmEnterpriseCertificateSecondDimension.Field.is_high_tech_enterprise] = 1
                if eaq_doc.get('eaqPublishDate'):
                    doc[DwmEnterpriseCertificateSecondDimension.Field.tech_certification_date] = eaq_doc['eaqPublishDate']
            # elif eaqType == '科技型中小企业':
            #     doc[DwmEnterpriseCertificateSecondDimension.Field.is_technologic] = 1
            elif eaqType == '专精特新小巨人':
                doc[DwmEnterpriseCertificateSecondDimension.Field.isSpecializedAndNewGiant] = 1
            elif eaqType == '专精特新中小企业':
                doc[DwmEnterpriseCertificateSecondDimension.Field.isSpecializedAndNew] = 1
        return doc
    else:
        result = {DwmEnterpriseCertificateSecondDimension.Field._id: key}
        eaqType = eaq_doc['eaqType']
        eaqPublishDate = eaq_doc['eaqPublishDate']
        if eaqType == '高新技术企业备案公示企业':
            result[DwmEnterpriseCertificateSecondDimension.Field.is_high_tech_enterprise] = 1
            if eaqPublishDate:
                result[DwmEnterpriseCertificateSecondDimension.Field.tech_certification_date] = eaqPublishDate
        # elif eaqType == '科技型中小企业':
        #     result[DwmEnterpriseCertificateSecondDimension.Field.is_technologic] = 1
        elif eaqType == '专精特新小巨人':
            result[DwmEnterpriseCertificateSecondDimension.Field.isSpecializedAndNewGiant] = 1
        elif eaqType == '专精特新中小企业':
            result[DwmEnterpriseCertificateSecondDimension.Field.isSpecializedAndNew] = 1
        if len(result) > 1:
            return result


def enterprise_annual_qualification_filter(doc):
    """
    数据过滤：
    1. 缺失关键字段
    2. 资质类别（eaqType）过滤
    3. 公告日期超过三年前
    4. 下架
    """
    for field in [
        OdsEnterpriseAnnualQualification.Field.eaqSource,
        OdsEnterpriseAnnualQualification.Field.nameId,
        OdsEnterpriseAnnualQualification.Field.eaqEnterpriseName,
        OdsEnterpriseAnnualQualification.Field.eaqType,
        OdsEnterpriseAnnualQualification.Field.eaqYear]:
        if not doc.get(field):
            return False
    if doc[OdsEnterpriseAnnualQualification.Field.eaqType] not in EAQ_TYPE_WHITELIST:
        return False
    if doc.get(OdsEnterpriseAnnualQualification.Field.eaqDelete) == '删除':
        return False
    return True
