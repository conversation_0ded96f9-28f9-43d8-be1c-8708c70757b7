# -*- coding: utf-8 -*-
"""
    dwm_enterprise_promotion_competitor
    ~~~~~~~
    
    Description
    
    :author: <PERSON>
    :copyright: (c) 2022, Tungee
    :date created: 2022-12-09
    :python version: 
"""
from tdr.common.constant.common import FormatType
from tdr.warehouse.dwd.dwd_enterprise.meta.dwd_enterprise import DwdEnterprise
from tdr.warehouse.ods.ods_promotion.meta.ods_promotion import OdsPromotion


class DwmEnterprisePromotionCompetitor(object):
    NAME = 'dwm_enterprise_promotion_competitor'

    FORMAT_TYPE = FormatType.json

    class Field:
        id = '_id'
        nameId = 'nameId'
        competitorId = 'competitorId'
        address = 'address'
        prmtKeys = 'prmtKeys'
        prmtSameKeysNumber = 'prmtSameKeysNumber'

    class AddressField:
        city = 'city'
        province = 'province'

    INPUTS = [OdsPromotion, DwdEnterprise]

    # Spark Schema
    SPARK_SCHEMA = {
        "fields": [
            {
                "metadata": {

                },
                "name": "_id",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "address",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "city",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "province",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "competitorId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "nameId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "prmtKeys",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "prmtSameKeysNumber",
                "nullable": True,
                "type": "long"
            }
        ],
        "type": "struct"
    }
