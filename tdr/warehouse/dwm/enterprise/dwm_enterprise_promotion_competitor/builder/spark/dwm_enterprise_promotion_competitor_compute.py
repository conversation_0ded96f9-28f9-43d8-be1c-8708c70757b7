# -*- coding: utf-8 -*-
"""
    dwm_enterprise_promotion_competitor_compute
    ~~~~~~~
    
    Description
    
    :author: <PERSON>
    :copyright: (c) 2022, Tungee
    :date created: 2022-12-12
    :python version: 
"""
import itertools
import sys

from pyspark.sql import SparkSession
from pyspark.sql import functions as F

from tdr.common.constant.module import ModuleNameConstant
from tdr.common.utils.md5_util import str_md5
from tdr.common.utils.spark.spark_utils import persist_rdd
from tdr.warehouse.dwd.dwd_enterprise.meta.dwd_enterprise import DwdEnterprise
from tdr.warehouse.dwm.enterprise.dwm_enterprise_promotion_competitor.meta.dwm_enterprise_promotion_competitor import \
    DwmEnterprisePromotionCompetitor
from tdr.warehouse.ods.ods_promotion.meta.ods_promotion import OdsPromotion
from tdr.warehouse.utils.spark import get_rdd_from_file

JOB_NAME = 'DW_{}_{}'.format(ModuleNameConstant.enterprise, DwmEnterprisePromotionCompetitor.NAME)


def promotion_filter(promotion_dict):
    """
    过滤数据：缺少企业映射、缺少推广源、缺少推广关键词
    """
    for k in [OdsPromotion.Field.nameId, OdsPromotion.Field.prmtLink]:
        if k not in promotion_dict:
            return False
    return True


def promotion_flat_map(promotion_dict):
    """
    key, (name_id, prmtTime)
    """
    name_id = promotion_dict[OdsPromotion.Field.nameId]
    key = promotion_dict[OdsPromotion.Field.prmtKey]
    time = promotion_dict.get(OdsPromotion.Field.prmtTime)
    return key, [(name_id, time)]


def cal_promotion_competitor(data):
    """
    计算竞对关系
    nameId, {'prmtSameKeysNumber': 1, competitor_id: [{'prmtKey': prmt_key, 'prmtTime': prmtTime}]}
    """
    prmt_key, ent_info_list = data  # (prmt_key, [(nameId, prmtTime)])
    # 去同个企业的重复推广词
    ent_info_list = sorted(ent_info_list, key=lambda k: k[1], reverse=True)
    unique_ent = list()
    name_id_set = set()
    for ent in ent_info_list:
        name_id = ent[0]
        if name_id not in name_id_set:
            unique_ent.append(ent)
            name_id_set.add(name_id)

    combinations = itertools.combinations(unique_ent, 2)
    for pair in combinations:
        first = pair[0]
        second = pair[1]
        if first[0] != second[0]:  # 不是同一个企业
            yield (first[0], second[0]), \
                {DwmEnterprisePromotionCompetitor.Field.prmtSameKeysNumber: 1,
                 DwmEnterprisePromotionCompetitor.Field.prmtKeys: [{'prmtKey': prmt_key, 'prmtTime': second[1]}]},
            yield (second[0], first[0]), \
                {DwmEnterprisePromotionCompetitor.Field.prmtSameKeysNumber: 1,
                 DwmEnterprisePromotionCompetitor.Field.prmtKeys: [{'prmtKey': prmt_key, 'prmtTime': first[1]}]},


def reduce_promotion_competitors(x_dict, y_dict):
    """
    合并同一个主体的所有竞对信息
    """
    prmt_same_keys_number_field = DwmEnterprisePromotionCompetitor.Field.prmtSameKeysNumber
    prmt_keys_field = DwmEnterprisePromotionCompetitor.Field.prmtKeys
    if x_dict and y_dict:
        x_dict[prmt_same_keys_number_field] = x_dict[prmt_same_keys_number_field] + y_dict[prmt_same_keys_number_field]
        x_dict[prmt_keys_field] = x_dict[prmt_keys_field] + y_dict[prmt_keys_field]
    x_dict[prmt_keys_field] = sorted(x_dict[prmt_keys_field], key=lambda k: k[u'prmtTime'], reverse=True)[0: 3]
    return x_dict


def format_competitor(data):
    (_id, competitor_id), prmt = data
    prmt[DwmEnterprisePromotionCompetitor.Field.nameId] = _id
    prmt[DwmEnterprisePromotionCompetitor.Field.competitorId] = competitor_id
    prmt_keys_field = DwmEnterprisePromotionCompetitor.Field.prmtKeys
    if prmt_keys_field in prmt:
        prmt[prmt_keys_field] = list(map(lambda x: x['prmtKey'], prmt[prmt_keys_field]))
    return competitor_id, prmt


def ent_address_map(doc):
    addr = doc.get(DwdEnterprise.Field.address) or {}
    province = addr.get('province')
    city = addr.get('city')
    address = {}
    if province:
        address['province'] = province
    if city:
        address['city'] = city
    if address:
        return doc[DwdEnterprise.Field.id], address


def fill_info(input_data):
    """
    1. 填充竞对的地址信息
    2. 生成 _id
    """
    _, (doc, address) = input_data
    if address:
        doc[DwmEnterprisePromotionCompetitor.Field.address] = address
    doc[DwmEnterprisePromotionCompetitor.Field.id] = \
        str_md5(doc[DwmEnterprisePromotionCompetitor.Field.nameId] + '_' + doc[DwmEnterprisePromotionCompetitor.Field.competitorId], digit=16)
    return doc


def run(*args):
    spark = SparkSession.builder.appName(JOB_NAME).getOrCreate()
    sc = spark.sparkContext
    ods_promotion_input, dwd_enterprise_input, dwm_enterprise_promotion_competitor_output, n_partition = args
    n_partition = int(n_partition)
    # ============= 读取数据 ============= #
    ods_promotion_rdd = get_rdd_from_file(sc, ods_promotion_input)
    dwd_enterprise_df = spark.read.parquet(dwd_enterprise_input)
    # ============== 加工过程 =============== #
    ent_address_rdd = dwd_enterprise_df.where(
        F.col(DwdEnterprise.Field.id).isNotNull() & F.col(DwdEnterprise.Field.address).isNotNull()
    ).select(
        DwdEnterprise.Field.id, DwdEnterprise.Field.address
    ).rdd.map(lambda row: ent_address_map(row.asDict(recursive=True))).filter(
        lambda doc: doc
    )

    dwm_enterprise_promotion_competitor_rdd = ods_promotion_rdd.filter(
        promotion_filter
    ).map(
        promotion_flat_map  # 按关键词展开推广信息
    ).reduceByKey(
        lambda x, y: x + y  # 合并同一个关键词的全部企业信息
    ).flatMap(
        cal_promotion_competitor  # 生成竞对记录
    ).reduceByKey(
        reduce_promotion_competitors  # 合并同对公司的所有推广记录
    ).map(
        format_competitor  # 格式化竞对记录
    ).leftOuterJoin(
        ent_address_rdd
    ).map(
        fill_info  # 补充竞对地址和生成 _id
    )

    # 截取企业的前1000条记录
    dwm_enterprise_promotion_competitor_rdd = dwm_enterprise_promotion_competitor_rdd.map(
        lambda doc: (doc['nameId'], [doc])
    ).reduceByKey(
        lambda x, y: x + y if len(x + y) <= 1000 else list(x+y)[:1000]
    ).flatMap(
        lambda data: data[1]
    )
    persist_rdd(dwm_enterprise_promotion_competitor_rdd, dwm_enterprise_promotion_competitor_output,
        numPartitions=n_partition)


if __name__ == '__main__':
    run(*sys.argv[1:])
