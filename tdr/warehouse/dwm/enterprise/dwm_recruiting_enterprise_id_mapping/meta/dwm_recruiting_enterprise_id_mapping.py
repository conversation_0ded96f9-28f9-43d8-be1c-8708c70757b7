#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    :File: dwm_recruiting_enterprise_id_mapping
    :author: <PERSON><PERSON><PERSON>
    :created: 2023/11/2 14:45
    :copyright: (c) 2023, Tungee
    :python version: python3
    :description: 
    
"""
from tdr.warehouse.ods.ods_recruiting.meta.ods_recruiting import OdsRecruiting


class DwmRecruitingEnterpriseIdMapping(object):
    # 表名
    NAME = 'dwm_recruiting_enterprise_id_mapping'

    # 字段名
    class Field:
        _id = '_id'
        recruitingSource = 'recruitingSource'
        recruitingEnterpriseId = 'recruitingEnterpriseId'
        nameId = 'nameId'

    # Spark Schema
    SPARK_SCHEMA = {
        "fields": [
            {
                "metadata": {

                },
                "name": "_id",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "recruitingSource",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "recruitingEnterpriseId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "nameId",
                "nullable": True,
                "type": "string"
            }
        ],
        "type": "struct"
    }

    INPUTS = [OdsRecruiting]
