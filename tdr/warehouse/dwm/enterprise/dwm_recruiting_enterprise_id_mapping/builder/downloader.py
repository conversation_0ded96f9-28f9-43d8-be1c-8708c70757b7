#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    :File: downloader
    :author: <PERSON><PERSON><PERSON>
    :created: 2023/11/2 15:33
    :copyright: (c) 2023, Tungee
    :python version: python3
    :description: 
    
"""
import click

from tdr.warehouse.common.warehouse import EnterpriseWarehouse
from tdr.warehouse.dwm.enterprise.dwm_recruiting_enterprise_id_mapping.meta.dwm_recruiting_enterprise_id_mapping import \
    DwmRecruitingEnterpriseIdMapping


@click.command()
@click.option('--version')
@click.option('--config')
def main(version, config):
    EnterpriseWarehouse(config).download(meta=DwmRecruitingEnterpriseIdMapping, version=version)


if __name__ == '__main__':
    main()
