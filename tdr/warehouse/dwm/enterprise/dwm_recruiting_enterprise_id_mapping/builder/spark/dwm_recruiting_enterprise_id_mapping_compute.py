#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    :File: dwm_recruiting_enterprise_id_mapping_compute
    :author: <PERSON><PERSON><PERSON>
    :created: 2023/11/2 15:11
    :copyright: (c) 2023, Tungee
    :python version: python3
    :description: 
    
"""
import sys

from pyspark.sql import SparkSession

from tdr.common.constant.module import ModuleNameConstant
from tdr.common.utils.spark.spark_utils import persist_rdd
from tdr.warehouse.dwm.enterprise.dwm_recruiting_enterprise_id_mapping.builder.util.dwm_recruiting_enterprise_id_mapping_util import \
    recruiting_check, cut_wanted_fields
from tdr.warehouse.dwm.enterprise.dwm_recruiting_enterprise_id_mapping.meta.dwm_recruiting_enterprise_id_mapping import \
    DwmRecruitingEnterpriseIdMapping
from tdr.warehouse.ods.ods_recruiting.meta.ods_recruiting import OdsRecruiting
from tdr.warehouse.utils.spark import get_rdd_from_file

JOB_NAME = 'DW_{}_{}'.format(ModuleNameConstant.enterprise, DwmRecruitingEnterpriseIdMapping.NAME)


def run(*args):
    spark = SparkSession.builder.appName(JOB_NAME).getOrCreate()
    sc = spark.sparkContext
    ods_recruiting_input, dwm_recruiting_enterprise_id_mapping_output, n_partition = args
    n_partition = int(n_partition)

    # 输入数据
    ods_recruiting_rdd = get_rdd_from_file(sc, ods_recruiting_input)

    dwm_df = ods_recruiting_rdd.filter(
        recruiting_check
    ).map(
        cut_wanted_fields
    ).toDF(schema=OdsRecruiting.SPARK_SCHEMA)

    df = dwm_df.dropDuplicates(subset=[OdsRecruiting.Field.recruitingEnterpriseId])
    df.coalesce(n_partition).write.mode('overwrite').json(dwm_recruiting_enterprise_id_mapping_output)


if __name__ == '__main__':
    run(*sys.argv[1:])
