#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    :File: dwm_recruiting_enterprise_id_mapping_util.py
    :author: <PERSON><PERSON><PERSON>
    :created: 2023/11/2 15:14
    :copyright: (c) 2023, Tungee
    :python version: python3
    :description: 
    
"""
from tdr.common.utils.dict_lib import cut_dict_fields
from tdr.warehouse.ods.ods_recruiting.meta.ods_recruiting import OdsRecruiting


def recruiting_check(doc):
    """
    基本校验
    @param doc:
    @return:
    """
    for field in [
        OdsRecruiting.Field.recruitingSource,
        OdsRecruiting.Field.recruitingEnterpriseId,
        OdsRecruiting.Field.nameId,
    ]:
        if doc.get(field) is None:
            return False
    if doc.get(OdsRecruiting.Field.recruitingExists) is False:
        return False
    if doc[OdsRecruiting.Field.recruitingSource] != 'zhipin':
        return False
    return True


def cut_wanted_fields(doc):
    """
    维度获取
    @param doc:
    @return:
    """
    result = cut_dict_fields(doc, [
        OdsRecruiting.Field.id,
        OdsRecruiting.Field.recruitingSource,
        OdsRecruiting.Field.recruitingEnterpriseId,
        OdsRecruiting.Field.nameId
    ])
    return result
