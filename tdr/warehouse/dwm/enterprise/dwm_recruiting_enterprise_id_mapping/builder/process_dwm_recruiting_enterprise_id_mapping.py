#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    :File: process_dwm_recruiting_enterprise_id_mapping
    :author: <PERSON><PERSON><PERSON>
    :created: 2023/11/2 15:11
    :copyright: (c) 2023, Tungee
    :python version: python3
    :description: 
    
"""
import click

from tdr.warehouse.common.warehouse import EnterpriseWarehouse
from tdr.warehouse.dwm.enterprise.dwm_recruiting_enterprise_id_mapping.meta.dwm_recruiting_enterprise_id_mapping import \
    DwmRecruitingEnterpriseIdMapping


@click.command()
@click.option('--version')
@click.option('--config')
@click.option('--driver-memory')
@click.option('--executor-cores')
@click.option('--num-executors')
@click.option('--executor-memory')
@click.option('--parallelism')
@click.option('--queue')
@click.option('--jars')
@click.option('--n_partition', default='16')
@click.option('--use_lakehouse', type=bool, default=True)
@click.option("--conf", "-c", default=None, multiple=True)
def main(version, config, n_partition, **kwargs):
    EnterpriseWarehouse(config).build(DwmRecruitingEnterpriseIdMapping, version, n_partition, **kwargs)


if __name__ == '__main__':
    main()
