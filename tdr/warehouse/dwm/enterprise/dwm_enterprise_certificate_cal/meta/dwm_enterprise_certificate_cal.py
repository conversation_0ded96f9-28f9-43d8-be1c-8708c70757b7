#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    ~~~~~~~~~~~~~~~~~~~~~~~

    Description of this file

    :author: l<PERSON><PERSON><PERSON>
    :copyright: (c) dwm_enterprise_certificate_cal.py, Tungee
    :date created: 2022/12/20
    :python version: 3.6

"""
from tdr.common.constant.common import FormatType
from tdr.common.constant.module import ModuleNameConstant
from tdr.warehouse.ods.ods_certificate.meta.ods_certificate import OdsCertificate
from tdr.warehouse.ods.ods_certification_authority.meta.ods_certification_authority import OdsCertificationAuthority
from tdr.warehouse.ods.ods_enterprise_certificate.meta.ods_enterprise_certificate import OdsEnterpriseCertificate


class DwmEnterpriseCertificateCal(object):
    NAME = 'dwm_enterprise_certificate_cal'

    FORMAT_TYPE = FormatType.json

    MODULE_NAME = ModuleNameConstant.enterprise

    class Field:
        _id = '_id'
        certId = 'certId'
        nameId = 'nameId'
        cert_name_list = 'certNameList'
        certAuthorityInfoList = 'certAuthorityInfoList'
        certBasicInfoList = 'certBasicInfoList'
        certSource = 'certSource'
        cert_type = 'certType'
        cert_expire_time = 'certExpireTime'
        cert_publish_time = 'certPublishTime'
        cal_cert_status = 'calCertStatus'
        is_latest_cert = "isLatestCert"
        is_last_expire_cert = "isLastExpireCert"
        cert_covered_num = 'certCoveredNum'  # 本证书体系覆盖人数
        cert_certification_basis = 'certCertificationBasis'  # 证书认证依据
        cert_recognition_mark = 'certRecognitionMark'  # 证书使用的认可标识
        to_be_renewed = 'toBeRenewed'
        ca_cancel_date = 'caCancelDate'  # 证书认证机构撤销时间
        ca_logout_date = 'caLogoutDate'  # 证书认证机构注销时间
        is_technologic = 'isTechnologic'
        is_history_technologic = 'isHistoryTechnologic'
        cert_authority = 'certAuthority'
        cert_standard = 'certStandard'  # 认证标准和技术要求

    # 输入
    INPUTS = [OdsCertificate, OdsEnterpriseCertificate, OdsCertificationAuthority]

    # Spark Schema
    SPARK_SCHEMA = {
        "fields": [
            {
                "metadata": {

                },
                "name": "_id",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caCancelDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caLogoutDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "calCertStatus",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certAcquireYear",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "max",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "min",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "certAcquireYearNum",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {

                },
                "name": "certAgencyContacts",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "contact",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "contactType",
                                "nullable": True,
                                "type": "long"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "certAgentInfoList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "key",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "value",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "certAlterInfoList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "containsNull": True,
                        "elementType": {
                            "fields": [
                                {
                                    "metadata": {

                                    },
                                    "name": "key",
                                    "nullable": True,
                                    "type": "string"
                                },
                                {
                                    "metadata": {

                                    },
                                    "name": "value",
                                    "nullable": True,
                                    "type": "string"
                                }
                            ],
                            "type": "struct"
                        },
                        "type": "array"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "certAmbassadornList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "key",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "value",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "certApplicant",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "cn",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "certApprovalId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certAttachments",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "description",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "link",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "name",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "type",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "url",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "certAuthority",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certAuthorityInfoList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "key",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "value",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "certBasicInfoList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "key",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "value",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "certBulkFoodSell",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {

                },
                "name": "certBusinessAddress",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "city",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "district",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "name",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "province",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "subdistrict",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "value",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "certBusinessMode",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certBusinessPrincipal",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "certCentralKitchen",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {

                },
                "name": "certCertificationBasis",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certCollectiveMealDistributionEntities",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {

                },
                "name": "certCommercialActivities",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certCoveredNum",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {

                },
                "name": "certDelete",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certDetailUpdateTime",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certEnterpriseAdministration",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certEnterpriseContacts",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "contact",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "contactName",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "contactNameMd5",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "contactPageImage",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "contactPageLink",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "contactType",
                                "nullable": True,
                                "type": "long"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "certEnterpriseFormerName",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "cn",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "certEnterpriseFranchiseStores",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "city",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "num",
                                "nullable": True,
                                "type": "long"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "certEnterpriseIndustry",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certEnterpriseInfoList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "key",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "value",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "certEnterpriseManagementResources",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "begin",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "brand",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "end",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "id",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nature",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "registerCategory",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "type",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "certEnterpriseName",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "cn",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "en",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameId",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameIdSource",
                                "nullable": True,
                                "type": {
                                    "fields": [
                                        {
                                            "metadata": {

                                            },
                                            "name": "name",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "nameId",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "source",
                                            "nullable": True,
                                            "type": "string"
                                        }
                                    ],
                                    "type": "struct"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "type",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "certEnterpriseOrgCode",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certEnterprisePrincipal",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "certEnterpriseRegNumber",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certEnterpriseRepresentative",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certEnterpriseSocialCreditCode",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certEnterpriseStaff",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {

                },
                "name": "certEnterpriseTotalAssets",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certEnterpriseTotalSales",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certEnterpriseTxjyInfo",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "id",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "time",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "certEnterpriseType",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certEquipmentModel",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certEquipmentName",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certExpireTime",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certIdOriginalValue",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certLicenseFiles",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "link",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "name",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "url",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "certManufacturer",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "cn",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "en",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "certManufacturerInfoList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "key",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "value",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "certNameFullTime",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certNameList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "certNetworkBusiness",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {

                },
                "name": "certOrgInfoList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "key",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "value",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "certOriginalPublishTime",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certPrincipalPerson",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certProduceAddress",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "city",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "district",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "province",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "subdistrict",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "value",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "certProductInfoList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "containsNull": True,
                        "elementType": {
                            "fields": [
                                {
                                    "metadata": {

                                    },
                                    "name": "key",
                                    "nullable": True,
                                    "type": "string"
                                },
                                {
                                    "metadata": {

                                    },
                                    "name": "value",
                                    "nullable": True,
                                    "type": "string"
                                }
                            ],
                            "type": "struct"
                        },
                        "type": "array"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "certProductName",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certProductType",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certProductTypeAndLabList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "key",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "value",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "certProducter",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "cn",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "en",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "nameId",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "nameIdSource",
                            "nullable": True,
                            "type": {
                                "fields": [
                                    {
                                        "metadata": {

                                        },
                                        "name": "name",
                                        "nullable": True,
                                        "type": "string"
                                    },
                                    {
                                        "metadata": {

                                        },
                                        "name": "nameId",
                                        "nullable": True,
                                        "type": "string"
                                    },
                                    {
                                        "metadata": {

                                        },
                                        "name": "source",
                                        "nullable": True,
                                        "type": "string"
                                    }
                                ],
                                "type": "struct"
                            }
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "certProducterInfoList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "key",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "value",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "certProductionEnterprise",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certProjectId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certPublicationId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certPublicationName",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certPublicationType",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certPublishTime",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certRecognitionMark",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certRecordAnnual",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certRectificationInfoList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "containsNull": True,
                        "elementType": {
                            "fields": [
                                {
                                    "metadata": {

                                    },
                                    "name": "key",
                                    "nullable": True,
                                    "type": "string"
                                },
                                {
                                    "metadata": {

                                    },
                                    "name": "value",
                                    "nullable": True,
                                    "type": "string"
                                }
                            ],
                            "type": "struct"
                        },
                        "type": "array"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "certRegion",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "city",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "district",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "name",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "province",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "subdistrict",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "value",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "certRemark",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certScope",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certSelfDeclarationInfoList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "key",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "value",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "certSelfDeclarationOrgInfoList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "key",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "value",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "certSelfDeclarationProductList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "containsNull": True,
                        "elementType": {
                            "fields": [
                                {
                                    "metadata": {

                                    },
                                    "name": "key",
                                    "nullable": True,
                                    "type": "string"
                                },
                                {
                                    "metadata": {

                                    },
                                    "name": "value",
                                    "nullable": True,
                                    "type": "string"
                                }
                            ],
                            "type": "struct"
                        },
                        "type": "array"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "certSource",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certSourceV2",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certStaffName",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certStaffSource",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certStaffType",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certStandard",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certStatus",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certStatusAlterReason",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certStatusAlterTime",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certSubitem",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certSupervisionAuthority",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certType",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "certUrl",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "cid",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "create_time",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "_spec_type",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "val",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "enterpriseSourceId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "import_update_time",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "_spec_type",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "val",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "isHistoryTechnologic",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {

                },
                "name": "isLastExpireCert",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {

                },
                "name": "isLatestCert",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {

                },
                "name": "last_update_time",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "_spec_type",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "val",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "nameId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "origin_urls",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "oss_urls",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "toBeRenewed",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {

                },
                "name": "update_time",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "_spec_type",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "val",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            }
        ],
        "type": "struct"
    }
