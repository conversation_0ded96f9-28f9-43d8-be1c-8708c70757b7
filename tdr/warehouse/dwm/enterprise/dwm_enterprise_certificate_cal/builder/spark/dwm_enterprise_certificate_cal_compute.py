#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    ~~~~~~~~~~~~~~~~~~~~~~~

    Description of this file

    :author: l<PERSON><PERSON><PERSON>
    :copyright: (c) dwm_enterprise_certificate_compute.py, Tungee
    :date created: 2022/12/19
    :python version: 3.6

"""
import sys

from pyspark import StorageLevel

from tdr.common.constant.module import ModuleNameConstant
from tdr.common.utils.spark.spark_utils import *

from tdr.warehouse.dwm.enterprise.dwm_enterprise_certificate_cal.utils.dwm_enterprise_certificate_util import *
from tdr.warehouse.ods.ods_certification_authority.meta.ods_certification_authority import OdsCertificationAuthority
JOB_NAME = 'DW_{}_{}'.format(ModuleNameConstant.enterprise, DwmEnterpriseCertificateCal.NAME)


def main(*args):
    [
        ods_certificate_input,
        ods_enterprise_certificate_input,
        ods_certification_authority_input,
        dwm_certificate_mongo_output,
        n_partition
    ] = args

    spark = SparkSession.builder.appName(JOB_NAME).getOrCreate()
    sc = spark.sparkContext
    n_partition = int(n_partition)

    ods_certificate_rdd = get_rdd_from_file(sc, ods_certificate_input)
    ods_enterprise_certificate_rdd = get_rdd_from_file(sc, ods_enterprise_certificate_input)
    ods_certification_authority_rdd = get_rdd_from_file(sc, ods_certification_authority_input)

    certificate_rdd = ods_certificate_rdd.filter(
        filter_certificate
    ).map(
        map_certificate_with_certid
    )

    enterprise_certificate_rdd = ods_enterprise_certificate_rdd.filter(
        filter_enterprise_certificate
    ).flatMap(
        flat_map_enterprise_certificate_with_certid
    )

    ca_name_dict = ods_certification_authority_rdd.filter(
        lambda doc: doc.get(OdsCertificationAuthority.Field.caName)
    ).map(
        process_mapping_data
    ).collectAsMap()
    bc_ca_name_map = sc.broadcast(ca_name_dict)

    all_certificate_rdd = sc.union(
        [certificate_rdd, enterprise_certificate_rdd]
    ).reduceByKey(
        lambda x, y: x + y
    ).map(
        map_certificate_with_certid_process
    ).flatMap(
        lambda doc: flat_map_certificate_before(doc, bc_ca_name_map)
    ).map(
        lambda doc: ('{}_{}'.format(doc[OdsCertificate.Field.nameId], doc.get(OdsCertificate.Field.certType, 'null')), [doc])
    ).reduceByKey(
        lambda x, y: x + y
    ).flatMap(
        cal_cert_dimension
    )
    all_certificate_rdd.persist(StorageLevel.MEMORY_AND_DISK)

    # 所有维度的数据输出
    persist_rdd(all_certificate_rdd, dwm_certificate_mongo_output, numPartitions=n_partition)


if __name__ == '__main__':
    main(*sys.argv[1:])