#!/usr/bin/env python
# -*- coding: utf-8 -*-
import re
from datetime import timedelta

from tdr.common.constant.common import is_valid
from tdr.common.utils.dict_lib import cut_dict_fields
from tdr.common.utils.md5_util import str_md5_base64
from tdr.common.utils.time_helper import convert_arbitrary_date_format, date_diff, is_leap_year
from tdr.warehouse.dwm.enterprise.dwm_enterprise_certificate_cal.meta.dwm_enterprise_certificate_cal import \
    DwmEnterpriseCertificateCal
from tdr.warehouse.dwm.enterprise.dwm_enterprise_certificate_cal.utils.constant import *
from tdr.warehouse.ods.ods_certificate.meta.ods_certificate import OdsCertificate
from tdr.warehouse.ods.ods_certification_authority.meta.ods_certification_authority import OdsCertificationAuthority
from tdr.warehouse.ods.ods_enterprise_certificate.meta.ods_enterprise_certificate import OdsEnterpriseCertificate


def filter_certificate(doc):
    """
    过滤证书数据
    1. 缺失关键字段
    2. certSource不在可选范围内
    3. 脏数据过滤
    4. 证书为删除状态且过期日期超过一年
    """
    for field in [OdsCertificate.Field.certEnterpriseName, OdsCertificate.Field.certId, OdsCertificate.Field.certSource]:
        if field not in doc:
            return False
    if doc[OdsCertificate.Field.certSource] not in CERTIFICATE_SOURCE_LIST:
        return False

    # jst.sc源目前只上两种类型数据
    # if source in ["jst.sc", "zfcxjst.hebei", "js.shaanxi"] \
    #         and doc.get('certType') not in [u"安全生产许可证", u"房地产开发企业资质"]:
    #     return False
    #
    # if source in ["zjt.liaoning", "zjt.hlj"] \
    #         and doc.get("certType") not in [u"安全生产许可证"]:
    #     return False
    # 以下证书类型将tyc来源从白名单去掉
    if (doc.get('certType') in TYC_REMOVE_TYPE or (doc.get('certNameList') and all([
        each in TYC_REMOVE_NAME for each in doc.get('certNameList')]))) \
            and doc[OdsCertificate.Field.certSource] == 'tyc':
        return False

    flag = False
    for name_doc in doc[OdsCertificate.Field.certEnterpriseName]:
        if isinstance(name_doc.get('nameId'), str) and \
                len(name_doc['nameId']) > 0:
            flag = True

    if flag is False:
        return False

    if doc.get(OdsCertificate.Field.certSourceV2) == "qcc" and doc.get(OdsCertificate.Field.certSource) == "cnca":
        # 内部库源解析有误，此处过滤掉
        qcc_type_whitelist = [
            '服务', '绿色', '节能', '环保', '良好', '农业', '食品',
            '体系认证', 'ccc', '产品认证', '有机产品', '控制点认证',
            '管理体系', '评价认证', '农产品', '森林认证', '环境标志产品',
            'CCC',
        ]
        is_cnca = False
        cert_type = doc.get(OdsCertificate.Field.certType) or ""
        for word in qcc_type_whitelist:
            if word in cert_type:
                is_cnca = True
                break
        if is_cnca is False:
            return False
    # expire_time = doc.get('certExpireTime') or ''
    # try:
    #     expire_time = convert_arbitrary_date_format(expire_time)
    # except Exception:
    #     pass
    # now = datetime.strptime(datetime.now().strftime('%Y-%m-%d'), '%Y-%m-%d')
    # if doc.get('certDelete') == '删除' and expire_time and expire_time < now - timedelta(days=365):
    #     return False
    return True


def map_certificate_with_certid(doc):
    # cqccms 强制赋值 CCC
    if doc.get(OdsCertificate.Field.certSource) == "cqccms" and not doc.get(OdsCertificate.Field.certType):
        doc[OdsCertificate.Field.certType] = "CCC"
    cert_id = re.sub(r'[\(\)（）\[\]【】〔〕{}〈〉<>\s+\\\.·:：/_—–-]', '', doc[OdsCertificate.Field.certId])
    return cert_id, [doc]


def filter_enterprise_certificate(doc):
    """
    过滤企业证书数据
    1. 缺失关键字段
    2. certSource不在可选范围内
    """
    for field in [OdsEnterpriseCertificate.Field.nameId, OdsEnterpriseCertificate.Field.certCertificateList, OdsEnterpriseCertificate.Field.certSource]:
        if field not in doc:
            return False
    if doc[OdsEnterpriseCertificate.Field.certSource] not in ENTERPRISE_CERTIFICATE_SOURCE_LIST:
        return False
    return True


def flat_map_enterprise_certificate_with_certid(doc):
    """
    企业证书数据根据证书编号展开
    """
    name_id = doc[OdsEnterpriseCertificate.Field.nameId]
    cert_source = doc[OdsEnterpriseCertificate.Field.certSource]
    cert_url = doc.get(OdsEnterpriseCertificate.Field.certUrl)
    cert_enterprise_social_credit_code = doc.get(OdsEnterpriseCertificate.Field.certEnterpriseSocialCreditCode)
    data = []
    if is_valid(doc, OdsEnterpriseCertificate.Field.certCertificateList, list):
        for cert in doc[OdsEnterpriseCertificate.Field.certCertificateList]:
            if isinstance(cert, dict):
                # 以下证书类型将tyc来源从白名单去掉
                if (cert.get('certType') in TYC_REMOVE_TYPE or (cert.get('certNameList') and all([
                    each in TYC_REMOVE_NAME for each in cert.get('certNameList')]))) and cert_source == 'tyc':
                    continue
                if cert.get('certId'):
                    cert['nameId'] = name_id
                    cert['certSource'] = cert_source
                    if cert_url:
                        cert['certUrl'] = cert_url
                    if cert_enterprise_social_credit_code:
                        cert['certEnterpriseSocialCreditCode'] = cert_enterprise_social_credit_code
                    cert_id = re.sub(r'[\(\)（）\[\]【】〔〕{}〈〉<>\s+\\\.·:：/_—–-]', '', cert['certId'])
                    cert['_id'] = str_md5_base64(cert_source + '_' + cert_id, 16)
                    cert['certEnterpriseName'] = [{
                        'nameId': name_id
                    }]
                    data.append((cert_id, [cert]))
    return data


def process_mapping_data(doc):
    doc = cut_dict_fields(doc, [
        OdsCertificationAuthority.Field.caName, OdsCertificationAuthority.Field.caApprovalId, OdsCertificationAuthority.Field.caExpireDate,
        OdsCertificationAuthority.Field.caStatus, OdsCertificationAuthority.Field.caAddress,
        OdsCertificationAuthority.Field.caInfoList, OdsCertificationAuthority.Field.caCancelDate, OdsCertificationAuthority.Field.caLogoutDate
    ])
    ca_info_list = doc.pop(OdsCertificationAuthority.Field.caInfoList, [])
    for ca_info in ca_info_list:
        if ca_info.get('key') == '网址':
            doc['netUrl'] = ca_info['value']
        if ca_info.get('key') == '认证类别及认证领域':
            doc['business'] = ca_info['value']
    address = doc.get(OdsCertificationAuthority.Field.caAddress, {})
    if address:
        doc[OdsCertificationAuthority.Field.caAddress] = address.get('value')
    return doc[OdsCertificationAuthority.Field.caName], doc


def choose_safe_cert_new(cert_list):
    """
     certExpireTime>certPublishTime
     :param cert_list:
     :return:
     """
    cert_list.sort(key=lambda x: x.get(OdsCertificate.Field.certExpireTime) or '1949-10-01', reverse=True)
    # 步骤一：1、2是否一致
    cert_map = {}
    for cert_detail in cert_list:
        if cert_detail.get(OdsCertificate.Field.certExpireTime) in cert_map:
            tmp_list = cert_map.get(cert_detail.get(OdsCertificate.Field.certExpireTime))
            tmp_list.append(cert_detail)
            cert_map[cert_detail.get(OdsCertificate.Field.certExpireTime)] = tmp_list
        else:
            cert_map[cert_detail.get(OdsCertificate.Field.certExpireTime)] = [cert_detail]
    cert_tmp_list = cert_map.get(cert_list[0].get(OdsCertificate.Field.certExpireTime))
    doc_val = None
    if len(cert_tmp_list) > 1:
        cert_tmp_2_list = []
        # 第二部判断：颁证时间是否有，优先取有的
        for cert_detail in cert_tmp_list:
            if cert_detail.get(OdsCertificate.Field.certPublishTime):
                cert_tmp_2_list.append(cert_detail)
        if len(cert_tmp_2_list) > 1:
            # 第三部判断：证书状态是否有，优先取有的
            cert_tmp_3_list = []
            for cert_detail in cert_tmp_2_list:
                if cert_detail.get(OdsCertificate.Field.certStatus) in {'有效', '无效'}:
                    cert_tmp_3_list.append(cert_detail)
            if len(cert_tmp_3_list) > 1:
                # 第四部判断：如果都有，优先取数据源高优先级的
                cert_tmp_3_list.sort(key=lambda x: ORDERED_SOURCE_LIST.index(x.get(OdsCertificate.Field.certSource)))
                doc_val = cert_tmp_3_list[0]
            elif len(cert_tmp_3_list) == 1:
                doc_val = cert_tmp_3_list[0]
            else:
                doc_val = cert_tmp_2_list[0]
        elif cert_tmp_2_list == 1:
            doc_val = cert_tmp_2_list[0]
        else:
            doc_val = cert_tmp_list[0]
    elif len(cert_tmp_list) == 1:
        doc_val = cert_list[0]
    if doc_val:
        return doc_val
    else:
        return cert_list[0]


def choose_architecture_cert(cert_list):
    """
    挑选建筑资质证书：取update_time大的
    :param cert_list:
    :return:
    """

    def cert_cmp(doc):
        default_time = '1970-01-19'
        return (-convert_arbitrary_date_format(doc[OdsCertificate.Field.certExpireTime]).timestamp()
                if OdsCertificate.Field.certExpireTime in doc
                else convert_arbitrary_date_format(default_time).timestamp(),
                ORDERED_SOURCE_LIST.index(doc[OdsCertificate.Field.certSource]))

    cert_list.sort(key=cert_cmp)
    return cert_list[0]


def map_certificate_with_certid_process(data):
    """
    多数据源同证书编号数据进行优先级挑选
    """
    key, cert_list = data[0], data[1]

    def source_cmp(doc):
        """排序规则:同证书编号有多个来源数据时，优先取过期日期最晚的，其次按照来源优先级取"""
        cert_expire_time = doc.get(OdsCertificate.Field.certExpireTime) or '1970-01-01'
        return cert_expire_time, -ORDERED_SOURCE_LIST.index(doc[OdsCertificate.Field.certSource]),

    if len(cert_list) > 1:
        flags = [item.get(OdsCertificate.Field.certType) == u"安全生产许可证" for item in cert_list]
        if all(flags):
            return choose_safe_cert_new(cert_list)
        # 建筑资质证书挑选：取update_time最新的
        is_architecture = False
        for c in cert_list:
            c_type = c.get(OdsCertificate.Field.certType)
            for n in c.get(OdsCertificate.Field.certNameList, []):
                if (c_type, n) in ARCHITECTURE_TYPE_NAME_SET:
                    is_architecture = True
                    break
            if is_architecture:
                break
        if is_architecture:
            return choose_architecture_cert(cert_list)

        cert_list.sort(key=source_cmp, reverse=True)

    # 优先级最高
    cert_doc = cert_list[0]
    return cert_doc


def get_cert_authority(info_list, default):
    if not info_list:
        return default
    for each in info_list:
        info_k = each.get('key')
        info_v = each.get('value')
        if info_k in ['发证机构名称', '机构名称']:
            return info_v
    return default


def mixed_authority(authority, cert_authority_info_list):
    if not cert_authority_info_list:
        for key, val in authority.items():
            if key in field_column_map:
                cert_authority_info_list.append({'key': field_column_map[key], 'value': val})
        return cert_authority_info_list
    for each in cert_authority_info_list:
        key = each.get('key')
        val = each.get('value')
        key = old_and_new_key_map.get(key, key)
        each['value'] = authority.get(column_field_map.get(key), val)
    return cert_authority_info_list


def flat_map_certificate_before(doc, bc_ca_name_map):
    """
    证书数据根据企业展开，证书数据处理
    证书状态计算、证书类型处理
    """
    # ==证书状态计算==
    cert_delete = doc.get(OdsCertificate.Field.certDelete)
    cert_basic_info_list = doc.get(OdsCertificate.Field.certBasicInfoList, [])
    now = datetime.strptime(datetime.now().strftime('%Y-%m-%d'), '%Y-%m-%d')
    expire_time = convert_arbitrary_date_format(doc.get(OdsCertificate.Field.certExpireTime))
    # 有效：证书在有效期，且证书在数据源无状态字段记录或者状态记录为有效，且证书未被置为 “删除”
    # 暂停：证书在有效期，且证书在数据源是暂停状态，且证书未被置为“删除”
    # 撤销：证书在有效期，且证书在数据源是撤销状态，且证书未被置为“删除”
    # 注销：证书在有效期，且证书在数据源是注销状态，且证书未被置为“删除”
    # 失效：证书已过有效期，且证书未被置为“删除”
    # 下架：证书被置为“删除”，且证书过期不超过365天
    cert_status = doc.get(OdsCertificate.Field.certStatus)
    if expire_time:
        # 判断是否证书被置为“删除”，如果是，根据数据有效期距离当前是否已过365天，已超过365天，则产品端不展示
        if cert_delete == '删除' and expire_time < now - timedelta(days=365):
            return []
        if cert_delete != '删除':
            if doc.get(OdsCertificate.Field.certSource) == 'cnca' and cert_status in {'注销', '撤销'}:
                doc[DwmEnterpriseCertificateCal.Field.cal_cert_status] = cert_status
            else:
                if expire_time >= now:
                    if cert_status is None or cert_status == '有效':
                        doc[DwmEnterpriseCertificateCal.Field.cal_cert_status] = '有效'
                    elif cert_status in STATUS_MAPPING:
                        doc[DwmEnterpriseCertificateCal.Field.cal_cert_status] = STATUS_MAPPING[cert_status]
                else:
                    doc[DwmEnterpriseCertificateCal.Field.cal_cert_status] = u"过期"
        else:
            doc[DwmEnterpriseCertificateCal.Field.cal_cert_status] = '失效'
        if doc.get(OdsCertificate.Field.certType) == '科技型中小企业' and expire_time >= now:
            doc[DwmEnterpriseCertificateCal.Field.is_technologic] = 1
        elif doc.get(OdsCertificate.Field.certType) == '科技型中小企业':
            doc[DwmEnterpriseCertificateCal.Field.is_history_technologic] = 1
    elif cert_status in STATUS_MAPPING:
        doc[DwmEnterpriseCertificateCal.Field.cal_cert_status] = STATUS_MAPPING[cert_status]
    elif doc.get(OdsCertificate.Field.certType) == '国产非特殊用途化妆品备案':
        beian_status = ''
        for basic in cert_basic_info_list:
            if isinstance(basic, dict) and basic.get('key') == '备案状态':
                beian_status = basic.get('value')
                break
        doc[DwmEnterpriseCertificateCal.Field.cal_cert_status] = STATUS_MAPPING2.get(beian_status, '有效')

    # 证书信息来源为（jzsc）,判断新数据certScope含有【施工劳务不分等级】但是certNameList里面没有，直接把这个名字加到certNameList里面
    cert_scope = doc.get(OdsCertificate.Field.certScope, '')
    cert_source = doc.get(OdsCertificate.Field.certSource)
    if cert_source == 'jzsc' and '施工劳务不分等级' in cert_scope \
            and '施工劳务不分等级' not in doc.get(OdsCertificate.Field.certNameList, []):
        lst = doc.get(OdsCertificate.Field.certNameList)
        if not lst:
            doc[DwmEnterpriseCertificateCal.Field.cert_name_list] = ['施工劳务不分等级']
        else:
            lst.append('施工劳务不分等级')

    # 清洗cert_name
    new_cert_name_list = []
    for cert_name in doc.get(OdsCertificate.Field.certNameList, []):
        cert_name = CERT_NAME_MAPPING.get(cert_name, cert_name)
        new_cert_name_list.append(cert_name)
    if new_cert_name_list:
        doc[DwmEnterpriseCertificateCal.Field.cert_name_list] = new_cert_name_list

    # 资质类别、名称做映射
    cert_type = doc.get(OdsCertificate.Field.certType)
    # 证书类型清洗
    if isinstance(cert_type, str):
        # 证书类别统一为英文括号、中文顿号
        cert_type = cert_type.replace('（', '(').replace('）', ')').replace('丶', '、')
        cert_type = CERT_TYPE_MAPPING.get(cert_type, cert_type)
        doc[DwmEnterpriseCertificateCal.Field.cert_type] = cert_type

    cert_type = doc.get(OdsCertificate.Field.certType)
    new_cert_type_list, new_cert_name_list = list(), list()
    for cert_name in doc.get(OdsCertificate.Field.certNameList, []):
        # 需原certType及原certNameList均满足条件则映射到分类
        if (cert_type, cert_name) in CERT_TYPE_AND_NAME_MAPPING:
            new_cert_type, new_cert_name = CERT_TYPE_AND_NAME_MAPPING[(cert_type, cert_name)]
            if new_cert_type:
                new_cert_type_list.append(new_cert_type)
            new_cert_name_list.append(new_cert_name or cert_name)
        # 仅需certType满足条件即可映射到分类
        elif CERT_TYPE_AND_NEW_CERT_TYPE_MAPPING.get(cert_type) is not None:
            new_cert_type_list.append(CERT_TYPE_AND_NEW_CERT_TYPE_MAPPING[cert_type])
        else:
            new_cert_name_list.append(cert_name)
    # 没有certNameList这个字段或为空列表的话，要对证书类别做下兜底映射，仅判断certType
    if not doc.get(OdsCertificate.Field.certNameList):
        # 仅需certType满足条件即可映射到分类
        if CERT_TYPE_AND_NEW_CERT_TYPE_MAPPING.get(cert_type) is not None:
            new_cert_type_list.append(CERT_TYPE_AND_NEW_CERT_TYPE_MAPPING[cert_type])

    if new_cert_type_list and len(set(new_cert_type_list)) == 1:
        # 仅当映射到唯一一个certType时更新certType
        doc[DwmEnterpriseCertificateCal.Field.cert_type] = new_cert_type_list[0]
    if cert_type == 'CMMI':
        if 'ML2' in str(cert_scope).upper():
            doc['certType'] = 'CMMI 2级'
        elif 'ML3' in str(cert_scope).upper():
            doc['certType'] = 'CMMI 3级'
        elif 'ML4' in str(cert_scope).upper():
            doc['certType'] = 'CMMI 4级'
        elif 'ML5' in str(cert_scope).upper():
            doc['certType'] = 'CMMI 5级'
        else:
            pass
    if new_cert_name_list:
        doc[DwmEnterpriseCertificateCal.Field.cert_name_list] = [i for i in set(new_cert_name_list) if i is not None]

    # CCC证书处理
    if cert_type == 'CCC':
        if doc.get(OdsCertificate.Field.certProductType) in CERT_PRODUCT_TYPES:
            doc[DwmEnterpriseCertificateCal.Field.cert_type] = doc[OdsCertificate.Field.certProductType]
        if not doc.get(OdsCertificate.Field.certBasicInfoList) and doc.get(OdsCertificate.Field.certSelfDeclarationInfoList):
            doc[DwmEnterpriseCertificateCal.Field.certBasicInfoList] = doc.get(OdsCertificate.Field.certSelfDeclarationInfoList)

    # ITSS证书处理
    if cert_type == 'ITSS':
        if cert_scope in ITSS_SECOND_TYPES:
            doc[DwmEnterpriseCertificateCal.Field.cert_type] = cert_scope
        elif cert_basic_info_list:
            for basic in cert_basic_info_list:
                if basic.get('key') == '业务领域' and basic.get('value') in ITSS_SECOND_TYPES:
                    doc[DwmEnterpriseCertificateCal.Field.cert_type] = basic['value']
                    break

    # CS证书处理
    if cert_type == 'CS':
        for basic in cert_basic_info_list:
            if basic.get('key') == '证书级别' and basic.get('value') in CS_SECOND_TYPES:
                doc[DwmEnterpriseCertificateCal.Field.cert_type] = basic['value']
                break

    # 承装（修、试）电力设施许可证证书处理
    if cert_type == '承装(修、试)电力设施许可证':
        doc[DwmEnterpriseCertificateCal.Field.cert_name_list] = [DLSS_CERT_NAME_MAPPING.get(item, item) for item in doc.get(OdsCertificate.Field.certNameList, [])]

    # 高新技术企业
    if doc.get(OdsCertificate.Field.certType) == '高新技术企业':
        cert_publish_time = doc.get(OdsCertificate.Field.certPublishTime)
        cert_expire_time = doc.get(OdsCertificate.Field.certExpireTime)
        days = 0
        if cert_publish_time and isinstance(cert_publish_time, str):
            if cert_expire_time and isinstance(cert_expire_time, str):
                days = date_diff(cert_publish_time, cert_expire_time)
            if days and days > 1097 or not cert_expire_time:
                year = int(cert_publish_time[:4]) + 3
                month_day = cert_publish_time[4:]
                if not is_leap_year(year) and month_day == '-02-29':
                    month_day = '-03-01'
                doc[DwmEnterpriseCertificateCal.Field.cert_expire_time] = str(year) + month_day

    # 测绘资质证书
    if doc.get(OdsCertificate.Field.certType) == '测绘资质证书':
        for basic in cert_basic_info_list:
            if basic['key'] == '资质等级':
                doc[DwmEnterpriseCertificateCal.Field.cert_type] = '测绘资质' + basic['value']
                doc[DwmEnterpriseCertificateCal.Field.cert_name_list] = [doc['certType']]
                break

    # ==发证机构维度计算==
    # 如果来源为cnca，则优先取certAuthorityInfoList，否则优先取certAuthority
    cert_authority_info_list = doc.get(OdsCertificate.Field.certAuthorityInfoList, [])
    cert_authority = doc.get(OdsCertificate.Field.certAuthority)
    if cert_source == 'cnca' or not cert_authority:
        cert_authority = get_cert_authority(cert_authority_info_list, cert_authority)
    if cert_authority:
        authority = bc_ca_name_map.value.get(cert_authority)
        if authority:
            ca_cancel_date, ca_logout_date = authority.get(OdsCertificationAuthority.Field.caCancelDate), authority.get(OdsCertificationAuthority.Field.caLogoutDate)
            if ca_cancel_date:
                doc[DwmEnterpriseCertificateCal.Field.ca_cancel_date] = ca_cancel_date
            if ca_logout_date:
                doc[DwmEnterpriseCertificateCal.Field.ca_logout_date] = ca_logout_date
            if cert_source == 'cnca':
                # 融合certAuthorityInfoList
                cert_authority_info_list = mixed_authority(authority, cert_authority_info_list)
                if cert_authority_info_list:
                    doc[DwmEnterpriseCertificateCal.Field.certAuthorityInfoList] = cert_authority_info_list

    # 源cnca新增"失效"定义：certBasicInfoList中 是否失效 值为 true，或 certAuthorityInfoList中 惩罚信息 / 惩罚原因 值包含关键词 撤销
    if doc.get(OdsCertificate.Field.certSource) == 'cnca':
        for basic in doc.get(OdsCertificate.Field.certBasicInfoList, []):
            if isinstance(basic, dict) and basic.get('key') == '是否失效' and basic.get('value') in ['true', 'True', '是', '失效', True]:
                doc[DwmEnterpriseCertificateCal.Field.cal_cert_status] = '失效'
                break
        for authority in doc.get(OdsCertificate.Field.certAuthorityInfoList, []):
            if authority.get('key') in {'惩罚信息', '惩罚原因'} and isinstance(authority.get('value'), str) and '撤销' in authority['value']:
                doc[DwmEnterpriseCertificateCal.Field.cal_cert_status] = '失效'
                break

    certificate_docs = []
    names = doc[OdsCertificate.Field.certEnterpriseName]
    id_set = set()
    for name_doc in names:
        if isinstance(name_doc.get(OdsCertificate.Field.nameId), str) and \
                len(name_doc[OdsCertificate.Field.nameId]) > 0:
            new_doc = doc.copy()
            new_id = doc[OdsCertificate.Field._id] + '_' + name_doc[OdsCertificate.Field.nameId]
            if new_id not in id_set:
                id_set.add(new_id)
                new_doc[DwmEnterpriseCertificateCal.Field._id] = new_id
                new_doc[DwmEnterpriseCertificateCal.Field.nameId] = name_doc[OdsCertificate.Field.nameId]
                certificate_docs.append(new_doc)
    return certificate_docs


def cal_cert_dimension(data):
    """
    同企业同证书类型下的证书维度计算
    """
    key, cert_list = data
    cert_type = key.split('_')[1]
    # 待续办证书标签计算
    if cert_type in RENEW_WHITE_LIST_TYPE and len(cert_list) >= 2:
        now = datetime.now()
        last_year_time = now - timedelta(days=365)
        target = None
        max_expire_time = datetime(1970, 1, 1)
        for doc in cert_list:
            expire_time = convert_arbitrary_date_format(doc.get(OdsCertificate.Field.certExpireTime))
            if expire_time is None:
                continue
            if target is None:
                target = doc
                max_expire_time = expire_time
            elif expire_time > max_expire_time:
                max_expire_time = expire_time
                target = doc
            elif expire_time == max_expire_time:
                if 'update_time' in doc and 'update_time' not in target:
                    target = doc
                elif 'update_time' in doc and 'update_time' in target:
                    if doc['update_time'] > target['update_time']:
                        target = doc
        if target is not None:
            if max_expire_time > now:
                if OdsCertificate.Field.certStatus in target and target.get(OdsCertificate.Field.certStatus) != '有效':
                    target[DwmEnterpriseCertificateCal.Field.to_be_renewed] = 1
            elif last_year_time <= max_expire_time <= now:
                target[DwmEnterpriseCertificateCal.Field.to_be_renewed] = 1

    if cert_type != "null":
        last_cert = sorted(cert_list, key=lambda e: (
            e.get(OdsCertificate.Field.certPublishTime, '1970-01-01'), e.get(OdsCertificate.Field.certExpireTime, '1970-01-01')), reverse=True)[0]
        # 是否最新颁发的证书
        last_cert[DwmEnterpriseCertificateCal.Field.is_latest_cert] = 1  # 传引用
        # 是否是最晚有效截止日期证书
        last_expire_cert = sorted(cert_list, key=lambda x: x.get(OdsCertificate.Field.certExpireTime, '1970-01-01'), reverse=True)[0]
        if last_expire_cert.get(OdsCertificate.Field.certExpireTime):
            last_expire_cert[DwmEnterpriseCertificateCal.Field.is_last_expire_cert] = 1

    # 计算 本证书体系覆盖人数，证书认证依据，证书使用的认可标识
    for cert in cert_list:
        cert_org_info_list = cert.get(OdsCertificate.Field.certOrgInfoList, [])
        cert_basic_info_list = cert.get(OdsCertificate.Field.certBasicInfoList, [])
        for row in cert_org_info_list:
            if row['key'] == '本证书体系覆盖人数' and row['value'] != '':
                cert[DwmEnterpriseCertificateCal.Field.cert_covered_num] = int(row['value'])
                break
        for row in cert_basic_info_list:
            if not isinstance(row, dict):
                continue
            if row['key'] == '认证依据':
                cert[DwmEnterpriseCertificateCal.Field.cert_certification_basis] = row['value']
            elif row['key'] == '证书使用的认可标识':
                cert[DwmEnterpriseCertificateCal.Field.cert_recognition_mark] = row['value']
            elif row['key'] == '认证标准和技术要求':
                cert[DwmEnterpriseCertificateCal.Field.cert_standard] = row['value']

    # 当证书编号带有*号时，与其他证书进行首尾字符匹配，如果首尾字符一致，且总位数一致，则认为是同一个证书记录，保留一个。优先保留不带*号的证书记录
    mark_cert_list = [cert for cert in cert_list if '*' in cert.get('certId')]
    _cert_list = [cert for cert in cert_list if '*' not in cert.get('certId')]
    for mark_cert in mark_cert_list:
        cert_id = re.sub(r'[\(\)（）\[\]【】〔〕{}〈〉<>\s+\\\.·:：/_—–-]', '', mark_cert['certId'])
        begin, end = re.split(pattern='\*+', string=cert_id, maxsplit=1)
        for cert in _cert_list:
            _cert_id = re.sub(r'[\(\)（）\[\]【】〔〕{}〈〉<>\s+\\\.·:：/_—–-]', '', cert['certId'])
            if _cert_id.startswith(begin) and _cert_id.endswith(end) and len(_cert_id) == len(cert_id):
                cert_list.remove(mark_cert)
                break

    return cert_list
