#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    
    @Time    : 2021/10/19 下午10:30
    <AUTHOR> <PERSON><PERSON><PERSON>
    @copyright: (c) 2021, <PERSON><PERSON><PERSON>
    @File    : recruiting_projection_compute
    @Software: PyCharm
"""
import sys

from pyspark import SparkConf, SparkContext, RDD

from tdr.common.utils.spark.spark_utils import get_rdd_from_file

FIELDS = ['nameId', 'recruitingName', 'recruitingSource']


def recruiting_filter(doc):
    if not all([field in doc for field in ['nameId', 'recruitingName']]):
        return False
    return True


def map_result(doc):
    name_id = doc.get('nameId', '')
    recruiting_name = doc.get('recruitingName', '')
    return '%s\t%s' % (name_id, recruiting_name)


def main(args):
    """
    企业数据精简
    """
    conf = SparkConf().setAppName('recruiting_projection_compute')
    spark_context = SparkContext.getOrCreate(conf=conf)
    # 加载pymongo_spark
    import pymongo_spark
    pymongo_spark.activate()

    [input_path, output_path] = args

    recruiting_rdd = get_rdd_from_file(spark_context, input_path, fields=FIELDS)
    assert isinstance(recruiting_rdd, RDD)
    recruiting_rdd.filter(
        lambda doc: all([field in doc for field in ['nameId', 'recruitingName']])
    ).map(
        map_result
    ).coalesce(1, True).sortBy(keyfunc=lambda x: x, numPartitions=1).saveAsTextFile(output_path)


if __name__ == '__main__':
    main(sys.argv[1:])
