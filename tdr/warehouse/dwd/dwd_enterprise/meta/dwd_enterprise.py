# -*- coding: utf-8 -*-
"""
    dwd_enterprise
    ~~~~~~~
    
    dwd_enterprise

    将内部库 enterprise 表的一些维度进行抽离，清理整理为 parquet 格式，以供后续流程使用
    
    :author: <PERSON>
    :copyright: (c) 2022, Tungee
    :date created: 2022-06-15
    :python version: 
"""
from tdr.common.constant.common import FormatType

from tdr.warehouse.ods.ods_enterprise.meta.ods_enterprise import OdsEnterprise


class DwdEnterprise(object):
    NAME = 'dwd_enterprise'

    FORMAT_TYPE = FormatType.parquet

    # 字段名
    class Field:
        id = '_id'
        name = 'name'
        equityPledgeList = 'equityPledgeList'
        desc = 'desc'
        foundTime = 'foundTime'
        businessTerm = 'businessTerm'
        branchOfficeList = 'branchOfficeList'
        creditChinaPubPunishmentList = 'creditChinaPubPunishmentList'
        capitalContributionList = 'capitalContributionList'
        punishmentList = 'punishmentList'
        registrationAuthority = 'registrationAuthority'
        approvalDate = 'approvalDate'
        checkInfoList = 'checkInfoList'
        entityAlgType = 'entityAlgType'
        legalRepresentative = 'legalRepresentative'
        socialCreditCode = 'socialCreditCode'
        formerNames = 'formerNames'
        anomalyList = 'anomalyList'
        iprPledgeList = 'iprPledgeList'
        mortgageInfoList = 'mortgageInfoList'
        orgCode = 'orgCode'
        memberList = 'memberList'
        alterationList = 'alterationList'
        operator = 'operator'
        homepage = 'homepage'
        address = 'address'
        regCapital = 'regCapital'
        holderList = 'holderList'
        enterpriseType = 'enterpriseType'
        industry = 'industry'
        industry_12315 = 'industry_12315'
        industry_tyc = 'industry_tyc'
        industry_qcc = 'industry_qcc'
        judicialAssistanceList = 'judicialAssistanceList'
        regNumber = 'regNumber'
        nameAlterList = 'nameAlterList'
        illegalInfoList = 'illegalInfoList'
        business = 'business'
        operStatus = 'operStatus'
        businessTags = 'businessTags'
        licenseList = 'licenseList'
        enterprisePubLicenseList = 'enterprisePubLicenseList'
        creditChinaPubLicenseList = 'creditChinaPubLicenseList'
        repeatNameId = 'repeatNameId'
        principal = 'principal'
        revokeReason = 'revokeReason'  # 注销原因
        deactivateReason = 'deactivateReason'  # 吊销原因
        isList = 'isList'  # 是否上市
        location = 'location'  # 注册地址坐标信息(目前此数据是使用注册地址信息调用高德接口获得的信息，精度为小数点后6位)
        heatBaidu = 'heatBaidu'  # 百度热度
        financingList = 'financingList'  # 融资列表
        operBrandList = 'operBrandList'  # 经营品牌列表
        businessType = 'businessType'  # 企业业务类型(映射表见企业业务类型映射)
        deactivateDate = 'deactivateDate'  # 吊销日期
        revokeDate = 'revokeDate'  # 注销日期
        licensingDate = 'licensingDate'  # 发照日期
        ICPList = 'ICPList'
        homepageExists = 'homepageExists'
        homepageReachability = 'homepageReachability'  # 官网是否可达
        homepageVerifyedByAttribute = 'homepageVerifyedByAttribute'  # 通过页面属性校验官网
        homepageVerifyedByICP = 'homepageVerifyedByICP'  # 通过多ICP校验官网
        # 加工字段
        name_alter_list = 'name_alter_list'
        officeAddress = 'officeAddress'
        isAnomaly = 'isAnomaly'  # 是否经营异常
        enName = 'enName'

    # 输入
    INPUTS = [OdsEnterprise]
