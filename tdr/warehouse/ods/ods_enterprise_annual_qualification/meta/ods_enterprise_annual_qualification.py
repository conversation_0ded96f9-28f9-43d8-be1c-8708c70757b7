# -*- coding: utf-8 -*-
"""
    ~~~~~~~~~~~~~~~~~~~~~~~
    企业资质表(年份)
    主键(eaqSource + eaqEnterpriseName + eaqType + eaqYear)
    Description of this file

    :author: <PERSON><PERSON><PERSON><PERSON>
    :copyright: (c) ods_enterprise_annual_qualification.py, Tungee
    :date created: 2022/12/19
    :python version: 3.6

"""
from tdr.common.constant.common import FormatType


class OdsEnterpriseAnnualQualification(object):
    NAME = 'ods_enterprise_annual_qualification'

    FORMAT_TYPE = FormatType.bson

    # 字段名
    class Field:
        _id = '_id'  # 主键(eaqSource + eaqEnterpriseName + eaqType + eaqYear)
        create_time = 'create_time'  #
        eaqAuthority = 'eaqAuthority'  # 认定机关
        eaqBeginDate = 'eaqBeginDate'  # 有效期开始时间
        eaqDelete = 'eaqDelete'  # 资质删除状态
        eaqEndDate = 'eaqEndDate'  # 有效期截止时间
        eaqEnterpriseName = 'eaqEnterpriseName'  # 企业名称
        eaqEnterpriseSocialCreditCode = 'eaqEnterpriseSocialCreditCode'  # 资质企业信用代码
        eaqId = 'eaqId'  # 资质编号
        eaqIdentificationType = 'eaqIdentificationType'  # 认定类型
        eaqNoticeType = 'eaqNoticeType'  # 公告类型
        eaqProducts = 'eaqProducts'  # 公司产品
        eaqPublishDate = 'eaqPublishDate'  # 公告日期
        eaqRecognitionLevel = 'eaqRecognitionLevel'  # 认定等级
        eaqRegion = 'eaqRegion'  # 发布地区
        eaqSource = 'eaqSource'  # 数据来源
        eaqTitle = 'eaqTitle'  # 公告标题
        eaqType = 'eaqType'  # 资质类型
        eaqUrl = 'eaqUrl'  # 公告链接
        eaqYear = 'eaqYear'  # 公告年份
        import_update_time = 'import_update_time'  #
        last_update_time = 'last_update_time'  #
        nameId = 'nameId'  # 企业ID
        nameIdSource = 'nameIdSource'  #
        update_time = 'update_time'  #

        class EaqRegionField:
            city = 'city'  # (optional) 城市
            district = 'district'  # (optional) 县或者区
            name = 'name'  # (optional) 映射标准值
            province = 'province'  # (optional) 省份
            value = 'value'  # 原始值

        class NameIdSourceField:
            name = 'name'  #
            nameId = 'nameId'  #
            source = 'source'  #

    # Spark Schema
    SPARK_SCHEMA = {
        "fields": [
            {
                "metadata": {
                    "comment": "主键(eaqSource + eaqEnterpriseName + eaqType + eaqYear)"
                },
                "name": "_id",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "数据来源"
                },
                "name": "eaqSource",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "企业名称"
                },
                "name": "eaqEnterpriseName",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": ""
                },
                "name": "eaqType",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": ""
                },
                "name": "create_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {
                    "comment": ""
                },
                "name": "import_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {
                    "comment": ""
                },
                "name": "last_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {
                    "comment": ""
                },
                "name": "nameId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": ""
                },
                "name": "nameIdSource",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "name",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "nameId",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "source",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {
                    "comment": "公告日期"
                },
                "name": "eaqPublishDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "公告年份"
                },
                "name": "eaqYear",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {
                    "comment": "公告标题"
                },
                "name": "eaqTitle",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "认定机关"
                },
                "name": "eaqAuthority",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "认定等级"
                },
                "name": "eaqRecognitionLevel",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "发布地区"
                },
                "name": "eaqRegion",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {
                                "comment": "(optional) 城市"
                            },
                            "name": "city",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": "(optional) 县或者区"
                            },
                            "name": "district",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": "(optional) 映射标准值"
                            },
                            "name": "name",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": "(optional) 省份"
                            },
                            "name": "province",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": "原始值"
                            },
                            "name": "value",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {
                    "comment": "有效期开始时间"
                },
                "name": "eaqBeginDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "有效期截止时间"
                },
                "name": "eaqEndDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "资质编号"
                },
                "name": "eaqId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "认定类型"
                },
                "name": "eaqIdentificationType",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "公告类型"
                },
                "name": "eaqNoticeType",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "公司产品"
                },
                "name": "eaqProducts",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": ""
                },
                "name": "update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {
                    "comment": "公告链接"
                },
                "name": "eaqUrl",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "资质删除状态"
                },
                "name": "eaqDelete",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "资质企业信用代码"
                },
                "name": "eaqEnterpriseSocialCreditCode",
                "nullable": True,
                "type": "string"
            }
        ],
        "type": "struct"
    }
