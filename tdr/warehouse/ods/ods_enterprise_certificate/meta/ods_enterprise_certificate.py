#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    ~~~~~~~~~~~~~~~~~~~~~~~
    企业证书表
    主键： certSource + name
    Description of this file

    :author: <PERSON><PERSON><PERSON><PERSON>
    :copyright: (c) ods_enterprise_certificate.py, Tungee
    :date created: 2022/12/19
    :python version: 3.6

"""
from tdr.common.constant.common import FormatType


class OdsEnterpriseCertificate(object):
    NAME = 'ods_enterprise_certificate'

    FORMAT_TYPE = FormatType.bson

    # 字段名
    class Field:
        _id = '_id'
        certCertificateList = 'certCertificateList'  # 资质证书列表(仅旧源数据残留（新数据已入certificate），后续不再使用)
        certEnterpriseSocialCreditCode = 'certEnterpriseSocialCreditCode'  # 证书企业信用代码
        certSource = 'certSource'  # 证书来源
        certUrl = 'certUrl'
        create_time = 'create_time'  # 创建时间
        ecCertList = 'ecCertList'  # 资质信息列表
        import_update_time = 'import_update_time'  # 导入更新时间
        last_update_time = 'last_update_time'  # 最近更新时间
        manageSystemCert = 'manageSystemCert'  # 管理体系认证
        name = 'name'  # 证书企业名
        nameId = 'nameId'  # 企业ID
        nameIdSource = 'nameIdSource'  # 映射来源
        update_time = 'update_time'  # 更新时间

    SPARK_SCHEMA = {
        "fields": [
            {
                "metadata": {
                    "comment": "主键： certSource + name"
                },
                "name": "_id",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "资质证书列表(仅旧源数据残留（新数据已入certificate），后续不再使用)"
                },
                "name": "certCertificateList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {
                                    "comment": "核发机关"
                                },
                                "name": "certAuthority",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "有效期至"
                                },
                                "name": "certExpireTime",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "证书编号"
                                },
                                "name": "certId",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "证书编号原始值"
                                },
                                "name": "certIdOriginalValue",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "资质名称列表"
                                },
                                "name": "certNameList",
                                "nullable": True,
                                "type": {
                                    "containsNull": True,
                                    "elementType": "string",
                                    "type": "array"
                                }
                            },
                            {
                                "metadata": {
                                    "comment": "发证日期"
                                },
                                "name": "certPublishTime",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "certRegion",
                                "nullable": True,
                                "type": {
                                    "fields": [
                                        {
                                            "metadata": {
                                                "comment": ""
                                            },
                                            "name": "city",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {
                                                "comment": ""
                                            },
                                            "name": "district",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {
                                                "comment": ""
                                            },
                                            "name": "province",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {
                                                "comment": ""
                                            },
                                            "name": "subdistrict",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {
                                                "comment": ""
                                            },
                                            "name": "value",
                                            "nullable": True,
                                            "type": "string"
                                        }
                                    ],
                                    "type": "struct"
                                }
                            },
                            {
                                "metadata": {
                                    "comment": "资质类别"
                                },
                                "name": "certType",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "certScope",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "certStatus",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "证书企业信用代码"
                },
                "name": "certEnterpriseSocialCreditCode",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "证书来源 [`jzsc`](http://jzsc.mohurd.gov.cn/home) [`glxy`](https://glxy.mot.gov.cn/person/index.do?type=1) [`rcpu`](https://rcpu.cwun.org/Index.aspx) [`xypt`](http://xypt.mwr.cn/)"
                },
                "name": "certSource",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "certUrl"
                },
                "name": "certUrl",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "创建时间"
                },
                "name": "create_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {
                    "comment": "资质信息列表"
                },
                "name": "ecCertList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {
                                    "comment": "批准文号   仅公路"
                                },
                                "name": "approvalNumber",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "核发机关"
                                },
                                "name": "certAuthority",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "资质承包类型"
                                },
                                "name": "certContractType",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "有效期至"
                                },
                                "name": "certExpireTime",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "证书编号"
                                },
                                "name": "certId",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "certIdOriginalValue",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "公路信用资质分类"
                                },
                                "name": "certKind",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "等级  Like：一级、二级、三级、甲级、不分等级 .."
                                },
                                "name": "certLevel",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "资质名称   专业类别/许可证（水利）  资质名称+资质类型（公路）"
                                },
                                "name": "certName",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "发证日期"
                                },
                                "name": "certPublishTime",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "备注   Like：增项资质、主项资质、其他资质、BG17C-1247（四库） ..."
                                },
                                "name": "certRemark",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "资质范围   四库证书详情"
                                },
                                "name": "certScope",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "资质子项  四库证书详情"
                                },
                                "name": "certSubitem",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "资质类别"
                                },
                                "name": "certType",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "定检结论  仅公路"
                                },
                                "name": "checkConclusion",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "证书状态"
                                },
                                "name": "certStatus",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "导入更新时间"
                },
                "name": "import_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {
                    "comment": "最近更新时间"
                },
                "name": "last_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {
                    "comment": "管理体系认证"
                },
                "name": "manageSystemCert",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {
                                    "comment": "有效期"
                                },
                                "name": "expireTime",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "名称"
                                },
                                "name": "name",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "审核日期"
                                },
                                "name": "reviewDate",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "证书企业名"
                },
                "name": "name",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "企业ID"
                },
                "name": "nameId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "映射来源"
                },
                "name": "nameIdSource",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "name",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "nameId",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "source",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {
                    "comment": "更新时间"
                },
                "name": "update_time",
                "nullable": True,
                "type": "timestamp"
            }
        ],
        "type": "struct"
    }
