# -*- coding: utf-8 -*-
"""
	ods_bankruptcy_reorganization
	由 TableMetaHelper 自动生成
	see https://gitlab.tangees.com/data-collector/enterprise-model/-/blob/master/validators/json-schema/bankruptcy_reorganization.json
	2023-02-01 15:54:28.756551
"""

from tdr.common.constant.common import FormatType


class OdsBankruptcyReorganization(object):
    NAME = 'ods_bankruptcy_reorganization'

    FORMAT_TYPE = FormatType.bson

    class Field:
        _id = '_id'  # 主键: brSource + brId
        brApplicantList = 'brApplicantList'  # 申请人列表
        brHandlingCourt = 'brHandlingCourt'  # 经办法院
        brId = 'brId'  # 案件编号
        brMainManager = 'brMainManager'  # 主要管理负责人
        brManagerOrganization = 'brManagerOrganization'  # 管理人机构
        brPublishDate = 'brPublishDate'  # 公开时间
        brRespondentList = 'brRespondentList'  # 被申请人列表
        brSource = 'brSource'  # 来源
        brType = 'brType'  # 案件类型
        brUrl = 'brUrl'  # 案件详情页URL
        create_time = 'create_time'  # 创建时间
        import_update_time = 'import_update_time'  # 导入更新时间
        last_update_time = 'last_update_time'  # 最近更新时间
        update_time = 'update_time'  # 更新时间

        class BrApplicantListField:
            name = 'name'  # 申请人
            nameId = 'nameId'  # 申请人主体映射
            nameIdSource = 'nameIdSource'  #

            class NameIdSourceField:
                name = 'name'  #
                nameId = 'nameId'  #
                source = 'source'  #

        class BrRespondentListField:
            name = 'name'  # 被申请人
            nameId = 'nameId'  # 被申请人主体映射
            nameIdSource = 'nameIdSource'  #

            class NameIdSourceField:
                name = 'name'  #
                nameId = 'nameId'  #
                source = 'source'  #

    SPARK_SCHEMA = {
        "fields": [
            {
                "metadata": {

                },
                "name": "_id",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "brApplicantList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "name",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameId",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameIdSource",
                                "nullable": True,
                                "type": {
                                    "fields": [
                                        {
                                            "metadata": {

                                            },
                                            "name": "name",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "nameId",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "source",
                                            "nullable": True,
                                            "type": "string"
                                        }
                                    ],
                                    "type": "struct"
                                }
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "brHandlingCourt",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "brId",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "brMainManager",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "brManagerOrganization",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "brPublishDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "brRespondentList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "name",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameId",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameIdSource",
                                "nullable": True,
                                "type": {
                                    "fields": [
                                        {
                                            "metadata": {

                                            },
                                            "name": "name",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "nameId",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "source",
                                            "nullable": True,
                                            "type": "string"
                                        }
                                    ],
                                    "type": "struct"
                                }
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "brSource",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "brType",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "brUrl",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "create_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "import_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "last_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "update_time",
                "nullable": True,
                "type": "timestamp"
            }
        ],
        "type": "struct"
    }
