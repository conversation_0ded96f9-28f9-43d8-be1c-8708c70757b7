# -*- coding: utf-8 -*-
"""
	:File: ods_court_announcement
	:Description: 由 TableMetaHelper 自动生成
	:See: https://gitlab.tangees.com/data-collector/enterprise-model/-/blob/master/validators/json-schema/court_announcement.json
	:Created: 2024-02-04 17:35:54.732160
"""

from tdr.common.constant.common import FormatType


class OdsCourtAnnouncement(object):
    NAME = 'ods_court_announcement'

    FORMAT_TYPE = FormatType.bson

    class Field:
        _id = '_id'  # 主键：caSource + caUrl
        caAddress = 'caAddress'  # 庭审地点
        caCaseReason = 'caCaseReason'  # 案由
        caCaseStatus = 'caCaseStatus'  # 案件状态
        caCaseType = 'caCaseType'  # 案件类型
        caContent = 'caContent'  # 公告内容 必须隐去身份证号码
        caDate = 'caDate'  # 庭审日期
        caDealGrade = 'caDealGrade'  # 处理等级名称
        caDefendant = 'caDefendant'  # 被告人列表
        caDeleted = 'caDeleted'  # 公告删除状态
        caDepartment = 'caDepartment'  # 承办部门
        caEndDate = 'caEndDate'  # 结案时间
        caJudgeAssistant = 'caJudgeAssistant'  # 法官助理
        caJudgeInfo = 'caJudgeInfo'  # 法官信息列表
        caLitigantList = 'caLitigantList'  # 当事人列表
        caPlaintiffList = 'caPlaintiffList'  # 起诉人列表
        caPublishDate = 'caPublishDate'  # 公告日期
        caPublishPage = 'caPublishPage'  # 公告版面
        caPublishUnit = 'caPublishUnit'  # 公告人 法院
        caRegion = 'caRegion'  # 地区
        caRelatedCaseNumber = 'caRelatedCaseNumber'  # 关联案号
        caServiceDate = 'caServiceDate'  # 公告送达日期
        caSource = 'caSource'  # 来源
        caStartDate = 'caStartDate'  # 立案时间
        caThird = 'caThird'  # 第三人
        caThirdList = 'caThirdList'  # 第三人人列表
        caTitle = 'caTitle'  # 公告标题
        caType = 'caType'  # 公告类型
        caUrl = 'caUrl'  # 公告落地页
        court_deleted = 'court_deleted'  # 云量子法院公告删除字段
        court_notice_deleted = 'court_notice_deleted'  # 云量子开庭公告删除字段
        create_time = 'create_time'  # 创建时间
        import_update_time = 'import_update_time'  # 导入更新时间
        is_content_changed = 'is_content_changed'  # 正文是否发生改变
        last_update_time = 'last_update_time'  # 最后更新时间
        update_time = 'update_time'  # 更新时间

    SPARK_SCHEMA = {
        "fields": [
            {
                "metadata": {

                },
                "name": "_id",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caAddress",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caCaseReason",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caCaseStatus",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caCaseType",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caContent",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caDealGrade",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caDefendant",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "name",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameId",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameIdSource",
                                "nullable": True,
                                "type": {
                                    "fields": [

                                    ],
                                    "type": "struct"
                                }
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "caDeleted",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caDepartment",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caEndDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caJudgeAssistant",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caJudgeInfo",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "judgeName",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "judgePhone",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "caLitigantList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "name",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameId",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameIdSource",
                                "nullable": True,
                                "type": {
                                    "fields": [

                                    ],
                                    "type": "struct"
                                }
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "caPlaintiffList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "name",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameId",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameIdSource",
                                "nullable": True,
                                "type": {
                                    "fields": [

                                    ],
                                    "type": "struct"
                                }
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "caPublishDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caPublishPage",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caPublishUnit",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caRegion",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "value",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "name",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "province",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "city",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "district",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "caRelatedCaseNumber",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caServiceDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caSource",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caStartDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caThird",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caThirdList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "name",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameId",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameIdSource",
                                "nullable": True,
                                "type": {
                                    "fields": [

                                    ],
                                    "type": "struct"
                                }
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "caTitle",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caType",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "caUrl",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "originCaUrl",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "court_deleted",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {

                },
                "name": "court_notice_deleted",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {

                },
                "name": "create_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "import_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "last_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "is_content_changed",
                "nullable": True,
                "type": "boolean"
            }
        ],
        "type": "struct"
    }
