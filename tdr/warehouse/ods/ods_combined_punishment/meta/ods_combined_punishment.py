# -*- coding: utf-8 -*-
"""
    ~~~~~~~~~~~~~~~~~~~~~~~
    行政处罚融合表
    Description of this file

    :author: l<PERSON><PERSON><PERSON>
    :copyright: (c) ods_combined_punishment.py, Tungee
    :date created: 2022/12/5
    :python version: 3.6

"""

from tdr.common.constant.common import FormatType


class OdsCombinedPunishment(object):
    NAME = 'ods_combined_punishment'

    FORMAT_TYPE = FormatType.bson

    class Field:
        _id = '_id'  # 主键：nameId + punishUuid
        create_time = 'create_time'  # 创建时间
        import_update_time = 'import_update_time'  # 导入更新时间
        last_update_time = 'last_update_time'  # 最近更新时间
        nameId = 'nameId'  # 企业映射ID
        originSource = 'originSource'  # 原始数据源
        punishAuthority = 'punishAuthority'  # 处罚机关
        punishBasis = 'punishBasis'  # 处罚依据
        punishConfiscation = 'punishConfiscation'  # 没收金额
        punishContent = 'punishContent'  # 处罚内容
        punishDecisionDate = 'punishDecisionDate'  # 处罚决定日期
        punishEndDate = 'punishEndDate'  # 公示截止期
        punishEnterpriseName = 'punishEnterpriseName'  # 企业名称
        punishEnterpriseScCode = 'punishEnterpriseScCode'  # 企业统一信用代码
        punishFileLink = 'punishFileLink'  # 处罚决定书链接
        punishId = 'punishId'  # 处罚文本号
        punishLicense = 'punishLicense'  # 暂扣或吊销证照名称及编号
        punishMethod = 'punishMethod'  # 处罚类型
        punishPenalty = 'punishPenalty'  # 罚款金额
        punishReason = 'punishReason'  # 处罚事由
        punishStatus = 'punishStatus'  # 数据状态（`0`为正常，`1`为下架，`2`为人工删除）
        punishType = 'punishType'  # 违法类型
        punishUuid = 'punishUuid'  # 清洗后的处罚文本号
        sourceIds = 'sourceIds'  # 融合数据来源
        update_time = 'update_time'  # 更新时间
        punishPublishDate = 'punishPublishDate'  # 公示日期

        class PunishConfiscationField:
            value = 'value'  # 金额
            unit = 'unit'  # 单位

        class PunishPenaltyField:
            value = 'value'  # 金额
            unit = 'unit'  # 单位

        class SourceIdsField:
            source = 'source'  # 表名+来源
            originSourceId = 'originSourceId'  # 原始来源表Id
            status = 'status'  # 数据源数据状态（`0`为正常，`1`为下架，`2`为人工删除）

    SPARK_SCHEMA = {
        "fields": [
            {
                "metadata": {

                },
                "name": "_id",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "create_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "import_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "punishStatus",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {

                },
                "name": "last_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "nameId",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "punishAuthority",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "punishBasis",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "punishContent",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "punishDecisionDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "punishEnterpriseName",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "punishEnterpriseScCode",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "punishFileLink",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "punishId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "punishUuid",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "punishMethod",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "punishReason",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "punishType",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "punishEndDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "punishPenalty",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "value",
                            "nullable": True,
                            "type": "double"
                        },
                        {
                            "metadata": {

                            },
                            "name": "unit",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "punishConfiscation",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "value",
                            "nullable": True,
                            "type": "double"
                        },
                        {
                            "metadata": {

                            },
                            "name": "unit",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "punishLicense",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "sourceIds",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "source",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "originSourceId",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "status",
                                "nullable": True,
                                "type": "long"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "originSource",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "punishPublishDate",
                "nullable": True,
                "type": "string"
            }
        ],
        "type": "struct"
    }
