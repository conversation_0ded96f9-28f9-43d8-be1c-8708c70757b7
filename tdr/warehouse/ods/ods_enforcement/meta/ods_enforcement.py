#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    ~~~~~~~~~~~~~~~~~~~~~~~
    执行信息表
    主键：efSource + efCaseNumber + efEnterpriseName
    Description of this file

    :author: <PERSON><PERSON><PERSON><PERSON>
    :copyright: (c) ods_enforcement.py, Tungee
    :date created: 2022/12/5
    :python version: 3.6

"""
from tdr.common.constant.common import FormatType


class OdsEnforcement(object):
    NAME = 'ods_enforcement'

    FORMAT_TYPE = FormatType.bson

    # 字段名
    class Field:
        efSource = 'efSource'  # 源
        efCaseNumber = 'efCaseNumber'  # 案号
        efUrl = 'efUrl'  # 执行信息落地页
        efEnterpriseName = 'efEnterpriseName'  # 被执行人名称
        efEnterpriseOrgCode = 'efEnterpriseOrgCode'  # 被执行人组织机构代码[如果是身份证号码，不存储]
        efExecutiveCourt = 'efExecutiveCourt'  # 执行法院
        efCaseCreateTime = 'efCaseCreateTime'  # 立案时间
        efCaseNumberDetail = 'efCaseNumberDetail'  # 案号 - 详情页
        efSubjectMoney = 'efSubjectMoney'  # 执行标的
        efPersonSex = 'efPersonSex'  # 被执行人性别
        efBasisId = 'efBasisId'  # 执行依据文号
        efState = 'efState'  # 执行状态
        efType = 'efType'  # 被执行人类型
        efStatus = 'efStatus'  # 被执行人官网是否存在
        efDishonestEnterpriseName = 'efDishonestEnterpriseName'  # 失信被执行人名称
        efDishonestEnterpriseOrgCode = 'efDishonestEnterpriseOrgCode'  # 失信被执行人组织机构代码[如果是身份证号码，不存储]
        efDishonestEnterprisePrincipal = 'efDishonestEnterprisePrincipal'  # 失信法人或负责人姓名
        efDishonestExecutiveCourt = 'efDishonestExecutiveCourt'  # 失信执行法院
        efDishonestProvince = 'efDishonestProvince'  # 省份
        efDishonestPursuantFileCode = 'efDishonestPursuantFileCode'  # 失信执行依据文号
        efDishonestCaseCreateTime = 'efDishonestCaseCreateTime'  # 失信立案时间
        efDishonestCaseNumber = 'efDishonestCaseNumber'  # 失信案号
        efDishonestJudgementUnit = 'efDishonestJudgementUnit'  # 失信做出执行依据单位
        efDishonestEnterpriseDuty = 'efDishonestEnterpriseDuty'  # 失信生效法律文书确定的义务
        efDishonestEnterprisePerformance = 'efDishonestEnterprisePerformance'  # 失信被执行人履行情况
        efDishonestEnterprisePerformanceDetail = 'efDishonestEnterprisePerformanceDetail'  # 失信被执行人行为具体情形
        efDishonestPublishTime = 'efDishonestPublishTime'  # 失信被执行信息发布时间
        efDishonestEnterpriseAge = 'efDishonestEnterpriseAge'  # 失信被执行人年龄
        efDishonestEnterpriseSex = 'efDishonestEnterpriseSex'  # 失信被执行人性别
        efDishonestJudgmentDocumentLink = 'efDishonestJudgmentDocumentLink'  # 失信裁决文书链接
        efDishonestStatus = 'efDishonestStatus'  # 失信执行人官网是否存在
        efTermCaseNumber = 'efTermCaseNumber'  # 终本案件案号
        efTermEnterpriseName = 'efTermEnterpriseName'  # 终本被执行人名称
        efTermExecutiveCourt = 'efTermExecutiveCourt'  # 终本执行法院
        efTermCaseCreateTime = 'efTermCaseCreateTime'  # 终本立案时间
        efTermDate = 'efTermDate'  # 终本日期
        efTermSubjectMoney = 'efTermSubjectMoney'  # 终本执行标的
        efTermDefaultSubjectMoney = 'efTermDefaultSubjectMoney'  # 终本未履行金额
        efTermEnterpriseSex = 'efTermEnterpriseSex'  # 终本被执行人性别
        efTermStatus = 'efTermStatus'  # 终本信息官网是否存在
        efLimitedPersonName = 'efLimitedPersonName'  # 限制消费人员姓名
        efLimitedPersonGender = 'efLimitedPersonGender'  # 限制消费人员性别
        efLimitedPersonExecutiveCourt = 'efLimitedPersonExecutiveCourt'  # 限制消费人员执行法院
        efLimitedPersonProvince = 'efLimitedPersonProvince'  # 限制消费人员省份
        efLimitedPersonCaseNumber = 'efLimitedPersonCaseNumber'  # 限制消费人员案号
        efLimitedPersonCaseCreateTime = 'efLimitedPersonCaseCreateTime'  # 限制消费人员立案时间
        efLimitedPersonCasePublishTime = 'efLimitedPersonCasePublishTime'  # 限制消费人员发布时间
        efLimitedPersonDocLink = 'efLimitedPersonDocLink'  # 限消令文书链接
        efLimitedPersonExecutiveCourtId = 'efLimitedPersonExecutiveCourtId'  # 限制消费人员执行法院代码
        efLimitedContent = 'efLimitedContent'  # 限制消费正文
        efLimitedContentPDF = 'efLimitedContentPDF'  # 限制消费pdf
        efLimitedStatus = 'efLimitedStatus'  # 限制消费官网是否存在
        efLimitedPersonEnterprise = 'efLimitedPersonEnterprise'  # 限制消费人员企业信息
        efLimitedApplicant = 'efLimitedApplicant'  # 限制消费执行申请人
        cid = 'cid'  # tyc company id(cid)
        nameId = 'nameId'
        efAvailableOnWebsite = 'efAvailableOnWebsite'

    class Status:
        status = 'status'  # 状态
        date = 'date'  # 校准时间

    SPARK_SCHEMA = {
        "fields": [
            {
                "metadata": {

                },
                "name": "_id",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "cid",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "create_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "efBasisId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efCaseCreateTime",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efCaseNumber",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efUrl",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efCaseNumberDetail",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efDishonestCaseCreateTime",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efDishonestCaseNumber",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efDishonestEnterpriseAge",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {

                },
                "name": "efDishonestEnterpriseDuty",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efDishonestEnterpriseName",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efDishonestEnterpriseOrgCode",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efDishonestEnterprisePerformance",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efDishonestEnterprisePerformanceDetail",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efDishonestEnterprisePrincipal",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efDishonestEnterpriseSex",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efDishonestExecutiveCourt",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efDishonestJudgementUnit",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efDishonestJudgmentDocumentLink",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efDishonestProvince",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efDishonestPublishTime",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efDishonestPursuantFileCode",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efDishonestStatus",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "date",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "status",
                            "nullable": True,
                            "type": "boolean"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "efEnterpriseName",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efEnterpriseOrgCode",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efExecutiveCourt",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efLimitedContent",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efLimitedContentPDF",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efLimitedPersonCaseCreateTime",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efLimitedPersonCaseNumber",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efLimitedPersonDocLink",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efLimitedPersonOssUrl",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efLimitedApplicant",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "name",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameId",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "efLimitedPersonEnterprise",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efLimitedPersonExecutiveCourt",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efLimitedPersonGender",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efLimitedPersonId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efLimitedPersonName",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efLimitedPersonProvince",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efLimitedStatus",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "date",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "status",
                            "nullable": True,
                            "type": "boolean"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "efPersonSex",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efSource",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efState",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efStatus",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "date",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "status",
                            "nullable": True,
                            "type": "boolean"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "efSubjectMoney",
                "nullable": True,
                "type": "double"
            },
            {
                "metadata": {

                },
                "name": "efTermCaseCreateTime",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efTermCaseNumber",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efTermDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efTermDefaultSubjectMoney",
                "nullable": True,
                "type": "double"
            },
            {
                "metadata": {

                },
                "name": "efTermEnterpriseName",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efTermEnterpriseOrgCode",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efTermEnterpriseSex",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efTermExecutiveCourt",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efTermStatus",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "date",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "status",
                            "nullable": True,
                            "type": "boolean"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "efLimitedPersonExecutiveCourtId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "efTermSubjectMoney",
                "nullable": True,
                "type": "double"
            },
            {
                "metadata": {

                },
                "name": "efType",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "import_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "last_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "nameId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "nameIdSource",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "name",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "nameId",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "source",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "efLimitedPersonCasePublishTime",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "efAvailableOnWebsite",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {

                },
                "name": "efTermBasisId",
                "nullable": True,
                "type": "string"
            },
        ],
        "type": "struct"
    }
