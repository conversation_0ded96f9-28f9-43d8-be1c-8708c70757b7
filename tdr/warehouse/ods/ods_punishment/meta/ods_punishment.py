#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    ~~~~~~~~~~~~~~~~~~~~~~~
    行政处罚表
    主键：punishSource + punishId
    Description of this file

    :author: <PERSON><PERSON><PERSON><PERSON>
    :copyright: (c) ods_punishment.py, Tungee
    :date created: 2022/12/5
    :python version: 3.6

"""
from tdr.common.constant.common import FormatType


class OdsPunishment(object):
    NAME = 'ods_punishment'

    FORMAT_TYPE = FormatType.bson

    # 字段名
    class Field:
        punishSource = 'punishSource'  # 来源
        punishEnterpriseName = 'punishEnterpriseName'  # 企业名称
        punishEnterpriseScCode = 'punishEnterpriseScCode'  # 企业统一信用代码
        punishContent = 'punishContent'  # 处罚内容
        punishId = 'punishId'  # 处罚文本号
        punishAuthority = 'punishAuthority'  # 处罚机关
        punishDecisionDate = 'punishDecisionDate'  # 处罚决定日期
        punishFileLink = 'punishFileLink'  # 处罚决定书链接
        punishType = 'punishType'  # 违法类型
        punishReason = 'punishReason'  # 处罚事由
        punishBasis = 'punishBasis'  # 处罚依据
        punishStatus = 'punishStatus'  # 处罚状态
        punishMethod = 'punishMethod'  # 处罚类别
        is_deleted = 'is_deleted'  # 是否删除  # 此字段存在且为1时，则表示整条数据删除不可用

    class PunishMethod:
        methodA = 'methodA'
        methodB = 'methodB'

    SPARK_SCHEMA = {
        "fields": [
            {
                "metadata": {

                },
                "name": "_id",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "cid",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "create_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "import_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "is_deleted",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {

                },
                "name": "last_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "nameId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "nameIdSource",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "name",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "nameId",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "source",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "punishAuthority",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "punishBasis",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "punishContent",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "punishDecisionDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "punishEnterpriseName",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "punishEnterpriseScCode",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "punishFileLink",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "punishId",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "punishMethod",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "methodA",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "methodB",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "punishReason",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "punishSource",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "punishStatus",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "punishType",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "update_source",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "punishEndDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "punishPenalty",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "value",
                            "nullable": True,
                            "type": "double"
                        },
                        {
                            "metadata": {

                            },
                            "name": "unit",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "punishConfiscation",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "value",
                            "nullable": True,
                            "type": "double"
                        },
                        {
                            "metadata": {

                            },
                            "name": "unit",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "punishLicense",
                "nullable": True,
                "type": "string"
            }
        ],
        "type": "struct"
    }
