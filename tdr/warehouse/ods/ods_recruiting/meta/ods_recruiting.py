#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    :File: ods_recruiting
    :author: <PERSON><PERSON><PERSON>
    :created: 2022/10/10 15:08
    :copyright: (c) 2021, Tungee
    :python version: 3
    :description: 
"""
import os

from tdr.common.constant.common import FormatType


class OdsRecruiting(object):
    NAME = 'ods_recruiting'

    FORMAT_TYPE = FormatType.bson

    # 字段名
    class Field:
        id = "_id"
        nameId = "nameId"
        name = 'name'
        recruitingExists = "recruitingExists"
        recruitingSource = "recruitingSource"
        recruitingName = "recruitingName"
        recruitingDesc = "recruitingDesc"
        recruitingPublishedTime = "recruitingPublishedTime"
        import_update_time = "import_update_time"
        recruitingEnterpriseName = "recruitingEnterpriseName"
        workingSalary = "workingSalary"
        workingSeniority = "workingSeniority"
        recruitingEducation = "recruitingEducation"
        recruitingCity = "recruitingCity"
        benefitList = "benefitList"
        recruitingDistrict = "recruitingDistrict"
        recruitingType = "recruitingType"
        recruitingCount = "recruitingCount"
        recruitingCategory = "recruitingCategory"
        workingAddress = "workingAddress"
        recruitingKeyword = "recruitingKeyword"
        recruitingSexRequest = "recruitingSexRequest"
        recruitingAgeRequest = "recruitingAgeRequest"
        recruitingDatePeriod = "recruitingDatePeriod"
        recruitingBusinessKeyWords = "recruitingBusinessKeyWords"
        recruitingPositionKeyWords = "recruitingPositionKeyWords"
        recruitingEnterpriseId = "recruitingEnterpriseId"
        expiredTime = "expiredTime"
        recruitingLanguage = "recruitingLanguage"
        recruitingDepartment = "recruitingDepartment"
        recruitingOriginPageUrl = "recruitingOriginPageUrl"
        recruitingCertificate = "recruitingCertificate"
        recruitingPageImage = "recruitingPageImage"
        recruitingPageImageTime = "recruitingPageImageTime"
        recruitingBaipinOriginPageUrl = "recruitingBaipinOriginPageUrl"
        recruitingContact = "recruitingContact"
        recruitingPageUrl = 'recruitingPageUrl'
        create_time = "create_time"

    # Spark Schema
    SPARK_SCHEMA = {
        "fields": [
            {
                "metadata": {
                    "comment": "主键 recruitingPageUrl"
                },
                "name": "_id",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "岗位福利列表"
                },
                "name": "benefitList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "创建时间"
                },
                "name": "create_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {
                    "comment": "岗位失效日期"
                },
                "name": "expiredTime",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "入库时标记字段"
                },
                "name": "has_recruiting_desc",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {
                    "comment": "导入更新时间"
                },
                "name": "import_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {
                    "comment": "联系方式同步辅助字段"
                },
                "name": "is_contact_changed",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {
                    "comment": "映射辅助字段"
                },
                "name": "is_name_changed",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {
                    "comment": "上次更新时间"
                },
                "name": "last_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {
                    "comment": "招聘企业映射名"
                },
                "name": "name",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "主键"
                },
                "name": "nameId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "映射记录字段"
                },
                "name": "nameIdSource",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "name",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "nameId",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "source",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {
                    "comment": "岗位年龄要求"
                },
                "name": "recruitingAgeRequest",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "max",
                            "nullable": True,
                            "type": "long"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "min",
                            "nullable": True,
                            "type": "long"
                        },
                        {
                            "metadata": {
                                "comment": "原始值，可能为`不限`"
                            },
                            "name": "value",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {
                    "comment": "定时映射时记录的字段"
                },
                "name": "recruitingAliasMap",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "alias",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "nameId",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {
                    "comment": "百度百聘原招聘链接(在爬取百度百聘的时候，存取它的来源链接)"
                },
                "name": "recruitingBaipinOriginPageUrl",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "招聘岗位业务关键词(由算法生成)"
                },
                "name": "recruitingBusinessKeyWords",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "职位类别"
                },
                "name": "recruitingCategory",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "注册情况"
                },
                "name": "recruitingCertificate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "招聘城市(由 recruitingDistrict 解析得到的二次维度)"
                },
                "name": "recruitingCity",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "联系方式"
                },
                "name": "recruitingContact",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "contact",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "contactName",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "contactName 加星前的 md5 值"
                                },
                                "name": "contactNameMd5",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "contactPageImage",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "contactPageLink",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "contactType",
                                "nullable": True,
                                "type": "long"
                            },
                            {
                                "metadata": {
                                    "comment": "招聘者的岗位"
                                },
                                "name": "position",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "招聘人数"
                },
                "name": "recruitingCount",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "max",
                            "nullable": True,
                            "type": "long"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "min",
                            "nullable": True,
                            "type": "long"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {
                    "comment": "招聘时间段"
                },
                "name": "recruitingDatePeriod",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "max",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "min",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {
                    "comment": "所属部门"
                },
                "name": "recruitingDepartment",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "职位描述"
                },
                "name": "recruitingDesc",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "工作地区"
                },
                "name": "recruitingDistrict",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {
                                "comment": "(optional) 城市"
                            },
                            "name": "city",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": "(optional) 区域：可能是二级市、县或者区"
                            },
                            "name": "district",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": "(optional) 映射标准值"
                            },
                            "name": "name",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": "(optional) 省份"
                            },
                            "name": "province",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "subdistrict",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": "原始值"
                            },
                            "name": "value",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {
                    "comment": "招聘学历要求"
                },
                "name": "recruitingEducation",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "招聘人未活跃天数"
                },
                "name": "recruitingInactiveDays",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "招聘人最后活跃时间"
                },
                "name": "recruiterLastActiveTime",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "招聘企业Id"
                },
                "name": "recruitingEnterpriseId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "招聘企业名称"
                },
                "name": "recruitingEnterpriseName",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "岗位是否存在(若岗位已下架，则此值为False)"
                },
                "name": "recruitingExists",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {
                    "comment": "职位关键字"
                },
                "name": "recruitingKeyword",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "使用语言"
                },
                "name": "recruitingLanguage",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "岗位名称"
                },
                "name": "recruitingName",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "原招聘页面地址(如果招聘地址跟 recruitingPageUrl 不一致，需要存本字段)"
                },
                "name": "recruitingOriginPageUrl",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "页面快照"
                },
                "name": "recruitingPageImage",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "页面快照时间"
                },
                "name": "recruitingPageImageTime",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "招聘页面地址"
                },
                "name": "recruitingPageUrl",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "招聘页面真实地址（目前只有职友集用到）"
                },
                "name": "recruiting_final_url",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "发布时间"
                },
                "name": "recruitingPublishedTime",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "岗位性别要求"
                },
                "name": "recruitingSexRequest",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "招聘岗位的[`来源`](https://gitlab.tangees.com/data-collector/tungee-parser/-/wikis/recruiting-dimensions#%E6%95%B0%E6%8D%AE%E6%BA%90)， 如：51job等"
                },
                "name": "recruitingSource",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "招聘类型(只能是兼职、全职、实习)"
                },
                "name": "recruitingType",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "入库时记录"
                },
                "name": "recruiting_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {
                    "comment": "更新时间"
                },
                "name": "update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {
                    "comment": "上班地址"
                },
                "name": "workingAddress",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {
                                "comment": "(optional) 城市"
                            },
                            "name": "city",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": "(optional) 区域：可能是二级市、县或者区"
                            },
                            "name": "district",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": "(optional) 映射标准值"
                            },
                            "name": "name",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": "(optional) 省份"
                            },
                            "name": "province",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "subdistrict",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": "原始值"
                            },
                            "name": "value",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {
                    "comment": "岗位薪水"
                },
                "name": "workingSalary",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "coinType",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "max",
                            "nullable": True,
                            "type": "double"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "min",
                            "nullable": True,
                            "type": "double"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "period",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "unit",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {
                    "comment": "工作经验"
                },
                "name": "workingSeniority",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "max",
                            "nullable": True,
                            "type": "long"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "min",
                            "nullable": True,
                            "type": "long"
                        }
                    ],
                    "type": "struct"
                }
            }
        ],
        "type": "struct"
    }
