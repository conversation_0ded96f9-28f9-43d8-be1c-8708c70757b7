#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
    ods_punish_dishonesty.py
    ~~~~~~~~~~~~~~~~~~~~~~~
    Description of this file
    
    :author: yatming
    :copyright: (c) 2023, Tungee
    :date created: 2023/12/18
    :python version: 3.6

"""
from tdr.common.constant.common import FormatType


class OdsPunishDishonesty(object):
    NAME = 'ods_punish_dishonesty'

    FORMAT_TYPE = FormatType.bson

    class Field:
        _id = '_id'  # 主键:weiboHomepage
        create_time = 'create_time'  # 创建时间
        import_update_time = 'import_update_time'  # 导入更新时间
        last_update_time = 'last_update_time'  # 上次更新时间
        nameId = 'nameId'  # 主键
        update_time = 'update_time'  # 更新时间
        pdAddress = 'pdAddress'  # 企业地址
        pdCompany = 'pdCompany'  # 企业名称
        pdCreditCode = 'pdCreditCode'  # 企业统一社会信用代码
        pdLawEnforcementAgency = 'pdLawEnforcementAgency'  # 执法单位
        pdPublishDate = 'pdPublishDate'  # 公布日期
        pdPunishBasis = 'pdPunishBasis'  # 处罚依据
        pdPunishDate = 'pdPunishDate'  # 移入日期
        pdPunishReason = 'pdPunishReason'  # 列入原因
        pdPunishResult = 'pdPunishResult'  # 处罚结果
        pdRemoveDate = 'pdRemoveDate'  # 移出日期
        pdRemoveReason = 'pdRemoveReason'  # 移出原因
        pdSource = 'pdSource'  # 网站来源
        pdType = 'pdType'  # 失信惩戒类型 1: 政府采购
        pdStatus = 'pdStatus'  # 数据状态: 0: 历史; 1: 有效; 2: 删除;
        pdUrl = 'pdUrl'  # 原文链接

        class PdAddressField:
            city = 'city'  # 城市
            district = 'district'  #
            name = 'name'  #
            province = 'province'  # 省份
            value = 'value'  # 原始地址字符串
            subdistrict = 'subdistrict'  # 乡/镇/街道

    SPARK_SCHEMA = {
        "fields": [
            {
                "metadata": {

                },
                "name": "_id",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "pdCompany",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "pdCreditCode",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "pdAddress",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "city",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "district",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "province",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "subdistrict",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "value",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "nameId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "pdPunishReason",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "pdPunishResult",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "pdPunishBasis",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "pdPunishDate",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "pdPublishDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "pdLawEnforcementAgency",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "pdRemoveReason",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "pdRemoveDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "pdType",
                "nullable": False,
                "type": "long"
            },
            {
                "metadata": {

                },
                "name": "pdStatus",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {

                },
                "name": "pdUrl",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "pdSource",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "create_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "import_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "last_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "update_time",
                "nullable": True,
                "type": "timestamp"
            }
        ],
        "type": "struct"
    }


if __name__ == '__main__':
    print(len(OdsPunishDishonesty.SPARK_SCHEMA['fields']))
