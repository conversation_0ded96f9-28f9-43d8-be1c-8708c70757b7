# -*- coding: utf-8 -*-
"""
    ods_promotion
    ~~~~~~~
    
    Description
    
    :author: <PERSON>
    :copyright: (c) 2022, Tungee
    :date created: 2022-12-08
    :python version: 
"""
from tdr.common.constant.common import FormatType


class OdsPromotion(object):
    NAME = 'ods_promotion'

    FORMAT_TYPE = FormatType.bson

    class Field:
        id = '_id'
        create_time = 'create_time'  # 创建时间
        has_fixed = 'has_fixed'  # 辅助字段
        import_update_time = 'import_update_time'  # 导入更新时间
        last_update_time = 'last_update_time'  # 最近更新时间
        name = 'name'  # 推广主体名称
        nameId = 'nameId'  # 主体映射id
        nameIdSource = 'nameIdSource'  # 映射源
        prmtDesc = 'prmtDesc'  # 推广描述
        prmtEmWords = 'prmtEmWords'  # 推广标题标红词
        prmtKey = 'prmtKey'  # 推广词
        prmtKeyword = 'prmtKeyword'  # 推广·关键词 已弃用
        prmtLink = 'prmtLink'  # 推广链接
        prmtLinkName = 'prmtLinkName'  # 推广链接名称
        prmtOriginLink = 'prmtOriginLink'  # 推广原始链接
        prmtRank = 'prmtRank'  # 推广主体评级
        prmtSnapshotList = 'prmtSnapshotList'  # 品专推广快照
        prmtSource = 'prmtSource'  # 推广来源
        prmtTime = 'prmtTime'  # 推广时间
        prmtTitle = 'prmtTitle'  # 推广标题
        prmtType = 'prmtType'  # 推广广告类型
        update_time = 'update_time'  # 更新时间

    SPARK_SCHEMA = {
        "fields": [
            {
                "metadata": {
                    "comment": "主键： prmtKey + prmtSource + prmtLink"
                },
                "name": "_id",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "创建时间"
                },
                "name": "create_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {
                    "comment": "辅助字段"
                },
                "name": "has_fixed",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {
                    "comment": "导入更新时间"
                },
                "name": "import_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {
                    "comment": "最近更新时间"
                },
                "name": "last_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {
                    "comment": "推广主体名称"
                },
                "name": "name",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "主体映射id"
                },
                "name": "nameId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "映射源"
                },
                "name": "nameIdSource",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "name",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "nameId",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "source",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {
                    "comment": "推广描述"
                },
                "name": "prmtDesc",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "推广标题标红词"
                },
                "name": "prmtEmWords",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "推广词"
                },
                "name": "prmtKey",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "推广·关键词 已弃用"
                },
                "name": "prmtKeyword",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "推广链接"
                },
                "name": "prmtLink",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "推广链接名称"
                },
                "name": "prmtLinkName",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "推广原始链接"
                },
                "name": "prmtOriginLink",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "推广主体评级"
                },
                "name": "prmtRank",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "品专推广快照"
                },
                "name": "prmtSnapshotList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "date",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "snapshot_url",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "推广来源 见[`推广来源映射`](https://gitlab.tangees.com/data-collector/tungee-parser/-/wikis/dimension-mapping#%E6%8E%A8%E5%B9%BF%E6%9D%A5%E6%BA%90%E6%98%A0%E5%B0%84)"
                },
                "name": "prmtSource",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "推广时间"
                },
                "name": "prmtTime",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "推广标题"
                },
                "name": "prmtTitle",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "推广广告类型"
                },
                "name": "prmtType",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "更新时间"
                },
                "name": "update_time",
                "nullable": True,
                "type": "timestamp"
            }
        ],
        "type": "struct"
    }
