#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    ~~~~~~~~~~~~~~~~~~~~~~~
    企业经营异常表
    主键： anomalySource + anomalyId
    Description of this file

    :author: l<PERSON><PERSON><PERSON>
    :copyright: (c) ods_enterprise_en_pinyin.py, Tungee
    :date created: 2022/12/5
    :python version: 3.6

"""
from tdr.common.constant.common import FormatType


class OdsEnterpriseAnomaly(object):
    NAME = 'ods_enterprise_anomaly'

    FORMAT_TYPE = FormatType.bson

    # 字段名
    class Field:
        name = "name"  # 企业名
        nameId = "nameId"  # 映射ID
        anomalyId = "anomalyId"  # 公告号
        anomalySource = "anomalySource"  # 数据来源
        anomalyCreateReason = "anomalyCreateReason"  # 列入原因
        anomalyCreateDate = "anomalyCreateDate"  # 列入日期
        anomalyCreateAuthority = "anomalyCreateAuthority"  # 作出决定机关(列入)
        anomalyRemoveReason = "anomalyRemoveReason"  # 移出原因
        anomalyRemoveDate = "anomalyRemoveDate"  # 移出日期
        anomalyRemoveAuthority = "anomalyRemoveAuthority"  # 作出决定机关(移出)

    SPARK_SCHEMA = {
        "fields": [
            {
                "metadata": {

                },
                "name": "_id",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "anomalyCreateAuthority",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "anomalyCreateDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "anomalyCreateReason",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "anomalyId",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "anomalyRemoveAuthority",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "anomalyRemoveDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "anomalyRemoveReason",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "anomalySource",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "create_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "import_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "last_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "name",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "nameId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "nameIdSource",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "name",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "nameId",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "source",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "update_time",
                "nullable": True,
                "type": "timestamp"
            }
        ],
        "type": "struct"
    }
