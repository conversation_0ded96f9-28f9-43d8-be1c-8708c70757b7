#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    ~~~~~~~~~~~~~~~~~~~~~~~
    认证机构表
    主键:caSource + caApprovalId
    Description of this file

    :author: l<PERSON><PERSON><PERSON>
    :copyright: (c) ods_certification_authority.py, Tungee
    :date created: 2022/12/19
    :python version: 3.6

"""
from tdr.common.constant.common import FormatType


class OdsCertificationAuthority(object):
    NAME = 'ods_certification_authority'

    FORMAT_TYPE = FormatType.bson

    # 字段名
    class Field:
        # https://gitlab.tangees.com/data-collector/enterprise-model/-/blob/master/label-wiki/certification_authority-dimensions.md
        _id = '_id'
        caAddress = 'caAddress'  # 机构地址
        caApprovalDate = 'caApprovalDate'  # 批准日期
        caApprovalId = 'caApprovalId'  # 机构批准号
        caCancelDate = 'caCancelDate'  # 撤销日期
        caExpireDate = 'caExpireDate'  # 有效截止日期
        caInfoList = 'caInfoList'  # 机构信息列表
        caLogoutDate = 'caLogoutDate'  # 注销日期
        caName = 'caName'  # 机构名称
        caSource = 'caSource'  # 数据源
        caStatus = 'caStatus'  # 机构状态
        caType = 'caType'  # 机构类别
        caUrl = 'caUrl'  # 落地页
        create_time = 'create_time'  # 创建时间
        import_update_time = 'import_update_time'  # 导入更新时间
        last_update_time = 'last_update_time'  # 上次更新时间
        update_time = 'update_time'  # 更新时间

    SPARK_SCHEMA = {
        "fields": [
            {
                "metadata": {
                    "comment": "主键:caSource + caApprovalId"
                },
                "name": "_id",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "机构地址"
                },
                "name": "caAddress",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "city",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "district",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "province",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "subdistrict",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "value",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {
                    "comment": "批准日期"
                },
                "name": "caApprovalDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "机构批准号"
                },
                "name": "caApprovalId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "撤销日期"
                },
                "name": "caCancelDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "有效截止日期"
                },
                "name": "caExpireDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "机构信息列表"
                },
                "name": "caInfoList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "key",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "value",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "注销日期"
                },
                "name": "caLogoutDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "机构名称"
                },
                "name": "caName",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "机构名称映射企业表ID"
                },
                "name": "nameId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "数据源"
                },
                "name": "caSource",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "机构状态"
                },
                "name": "caStatus",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "机构类别"
                },
                "name": "caType",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "落地页"
                },
                "name": "caUrl",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "创建时间"
                },
                "name": "create_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {
                    "comment": "导入更新时间"
                },
                "name": "import_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {
                    "comment": "上次更新时间"
                },
                "name": "last_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {
                    "comment": "更新时间"
                },
                "name": "update_time",
                "nullable": True,
                "type": "timestamp"
            }
        ],
        "type": "struct"
    }
