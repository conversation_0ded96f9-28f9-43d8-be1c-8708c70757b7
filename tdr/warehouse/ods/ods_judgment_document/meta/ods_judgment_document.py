#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    ~~~~~~~~~~~~~~~~~~~~~~~
    裁判文书表
    主键：jdSource + jdCaseNumber + jdType
    Description of this file

    :author: l<PERSON><PERSON><PERSON>
    :copyright: (c) ods_judgment_document.py, Tungee
    :date created: 2022/12/5
    :python version: 3.6

"""
from tdr.common.constant.common import FormatType


class OdsJudgmentDocument(object):
    NAME = 'ods_judgment_document'

    FORMAT_TYPE = FormatType.bson

    # 字段名
    class Field:
        jdSource = 'jdSource'  # 来源
        jdCaseOfAction = 'jdCaseOfAction'  # 案由
        jdTitle = 'jdTitle'  # 文书标题
        jdCaseType = 'jdCaseType'  # 案件类型
        jdTrialRound = 'jdTrialRound'  # 审判程序
        jdCaseNumber = 'jdCaseNumber'  # 案号
        jdCaseJudgmentDate = 'jdCaseJudgmentDate'  # 判决日期
        jdUrl = 'jdUrl'  # 文书链接
        jdPublishDate = 'jdPublishDate'  # 文书发布日期
        jdCourt = 'jdCourt'  # 判决法院
        jdType = 'jdType'  # 文书类型
        jdCaseRelatedPersonList = 'jdCaseRelatedPersonList'  # 案件相关人员
        jdCaseParties = 'jdCaseParties'  # 案件当事人信息
        jdContent = 'jdContent'  # 文书详细信息(来源是ylz时才有)【必须隐去身份证号码】
        jdPageImage = 'jdPageImage'  # 文书详情页快照
        jdCaseResult = 'jdCaseResult'  # 案件判决结果
        jdAppealRecord = 'jdAppealRecord'  # 上诉记录
        jdFact = 'jdFact'  # 判决事实
        jdReason = 'jdReason'  # 判决理由
        jdLegalBasis = 'jdLegalBasis'  # 法律依据
        jdDelete = 'jdDelete'  # 广告删除状态
        c_id = 'c_id'  # ylz文书id

    class JdCaseOfAction:
        firstCause = 'firstCause'
        secondCause = 'secondCause'
        thirdCause = 'thirdCause'
        fourthCause = 'fourthCause'
        fifthCause = 'fifthCause'

    class JdCaseRelatedPersonList:
        type = 'type'  # 类型 type可能为空字符串
        name = 'name'  # 名称
        nameId = 'nameId'  # 映射用

    SPARK_SCHEMA = {
        "fields": [
            {
                "metadata": {

                },
                "name": "_id",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "create_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "import_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "jdPageImage",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "c_id",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "jdAppealRecord",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "jdCaseJudgmentDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "jdCaseNumber",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "jdCaseOfAction",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "fifthCause",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "firstCause",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "fourthCause",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "secondCause",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "thirdCause",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "jdCaseParties",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "jdCaseRelatedPersonList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "name",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameId",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameIdSource",
                                "nullable": True,
                                "type": {
                                    "fields": [
                                        {
                                            "metadata": {

                                            },
                                            "name": "nameId",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "name",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "source",
                                            "nullable": True,
                                            "type": "string"
                                        }
                                    ],
                                    "type": "struct"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "type",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "jdCaseResult",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "jdCaseType",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "jdContent",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "jdCourt",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "jdDeleted",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "jdFact",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "jdLegalBasis",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "jdPublishDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "jdReason",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "jdSource",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "jdTitle",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "jdTrialRound",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "jdType",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "jdUrl",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "last_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "jdCaseAmount",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "value",
                            "nullable": True,
                            "type": "double"
                        },
                        {
                            "metadata": {

                            },
                            "name": "coinType",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "source",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "jdLitigationResult",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "name",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "litigationStatus",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameId",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "amount",
                                "nullable": True,
                                "type": {
                                    "fields": [
                                        {
                                            "metadata": {

                                            },
                                            "name": "value",
                                            "nullable": True,
                                            "type": "double"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "coinType",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "source",
                                            "nullable": True,
                                            "type": "string"
                                        }
                                    ],
                                    "type": "struct"
                                }
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            }
        ],
        "type": "struct"
    }
