# -*- coding: utf-8 -*-
"""
    ods_enterprise
    由 TableMetaHelper 自动生成

    2023-01-12 16:31:30.768485
"""
from tdr.common.constant.common import FormatType


class OdsEnterprise(object):
    NAME = 'ods_enterprise'

    FORMAT_TYPE = FormatType.bson

    class Field:
        BdAuthenticationCode = 'BdAuthenticationCode'  # 百度认证识别码
        BdAuthenticationTypeList = 'BdAuthenticationTypeList'  # 百度认证类型列表
        BdCooperationTime = 'BdCooperationTime'  # 百度认证合作时间
        BdSiteName = 'BdSiteName'  # 百度认证网址名称
        BdWeblink = 'BdWeblink'  # 百度认证网址
        _id = '_id'  # 主键
        _searchKey = '_searchKey'  # 辅助字段
        _searchType = '_searchType'  # 辅助字段
        _spiderIcComplete = '_spiderIcComplete'  # 辅助字段
        address = 'address'  # [`注册地址`](https://gitlab.tangees.com/data-collector/tungee-parser/-/wikis/dimension-check-rules#address)
        agentSystemList = 'agentSystemList'  # 代理产品系统列表(可选 ios 或 android)
        alterationList = 'alterationList'  # 变更信息列表
        alteration_list_update_time = 'alteration_list_update_time'  # (辅助字段)alterationList更新时间
        anomalyList = 'anomalyList'  # 经营异常列表
        anomalySource = 'anomalySource'  # 经营异常来源
        anomaly_list_update_time = 'anomaly_list_update_time'  # (辅助字段)经营异常列表更新时间
        app_update_time = 'app_update_time'  # (辅助字段)app爬取时间
        baiduMapAddress = 'baiduMapAddress'  # [`百度地图映射企业地址`](https://gitlab.tangees.com/data-collector/tungee-parser/-/wikis/dimension-check-rules#baiduMapAddress)
        branchOfficeList = 'branchOfficeList'  # [`分公司列表`](https://gitlab.tangees.com/data-collector/tungee-parser/-/wikis/dimension-check-rules#branchofficelist)
        business = 'business'  # [`主营业务`](https://gitlab.tangees.com/data-collector/tungee-parser/-/wikis/dimension-check-rules#business)
        businessTags = 'businessTags'  # 主营业务标签(该维度是算法生成的)
        businessTerm = 'businessTerm'  # [`营业期限`](https://gitlab.tangees.com/data-collector/tungee-parser/-/wikis/dimension-check-rules#businessterm)(min 和 max 至少要存在一个，max 可以取一个特殊值 永续经营。在工商更新模块中，若不存在则为空字典)
        businessType = 'businessType'  # 企业业务类型(映射表见[`企业业务类型映射`](https://gitlab.tangees.com/data-collector/tungee-parser/-/wikis/dimension-mapping#%E4%BC%81%E4%B8%9A%E4%B8%9A%E5%8A%A1%E7%B1%BB%E5%9E%8B))
        cancellationList = 'cancellationList'  # 注销备案/公告信息
        capitalContributionList = 'capitalContributionList'  # 企业公示股东出资列表
        checkInfoList = 'checkInfoList'  # 抽查检查信息
        cid = 'cid'  # 年报计算用到
        companyAddress = 'companyAddress'  # data_quality目录下用到
        composition = 'composition'  # 组成形式(只在个体工商户中存在)
        crawl_source = 'crawl_source'  # 12个解析有在用
        create_time = 'create_time'  # 创建时间
        creditChinaPubLicenseList = 'creditChinaPubLicenseList'  # 行政许可列表(信用中国)
        creditChinaPubPunishmentList = 'creditChinaPubPunishmentList'  # 行政处罚列表(信用中国)
        deactivateDate = 'deactivateDate'  # 吊销日期
        deactivateReason = 'deactivateReason'  # 吊销原因
        desc = 'desc'  # 企业简介
        devSystemList = 'devSystemList'  # 自研产品系统列表(可选 ios 或 android)
        enName = 'enName'  # 英文名
        enterpriseChannelList = 'enterpriseChannelList'  # 渠道列表
        enterpriseContactList = 'enterpriseContactList'  # 企业联系方式列表(目前只在珠海商事主体源中有解析该字段)
        enterpriseExists = 'enterpriseExists'  # 企业是否存在
        enterprisePubLicenseList = 'enterprisePubLicenseList'  # 行政许可列表(企业公示)
        enterpriseType = 'enterpriseType'  # [`企业类型`](https://gitlab.tangees.com/data-collector/tungee-parser/-/wikis/dimension-check-rules#enterprisetype)
        entityAlgType = 'entityAlgType'  # 实体类型算法识别结果(算法识别结果: 0渠道，1工厂, 2非渠道非工厂, 3既是渠道又是工厂)
        equityAlterList = 'equityAlterList'  # 股权变更列表
        equityPledgeList = 'equityPledgeList'  # 股权出质登记信息
        executivePartner = 'executivePartner'  # 执行事务合伙人(合伙企业独有)
        extractBranchOfficeList = 'extractBranchOfficeList'  # 新成立分公司列表(新成立分公司匹配上总公司，在总公司加上extractBranchOfficeList记录该新成立分公司)
        financingList = 'financingList'  # [`融资列表`](https://gitlab.tangees.com/data-collector/tungee-parser/-/wikis/dimension-check-rules#finacinglist)
        formerNames = 'formerNames'  # 曾用名
        foundTime = 'foundTime'  # [`成立时间`](https://gitlab.tangees.com/data-collector/tungee-parser/-/wikis/dimension-check-rules#foundtime)
        gsxtEntType = 'gsxtEntType'  # (企业类型) 工商系统数据
        gsxtNodeNum = 'gsxtNodeNum'  # 省份代码,工商系统数据
        gsxtPripId = 'gsxtPripId'  # 工商系统数据
        hasBdPromotion = 'hasBdPromotion'  # 该企业是否开通百度信誉档案
        hasDesc = 'hasDesc'  # 是否有百度简介
        hasHisBdPromotion = 'hasHisBdPromotion'  # pytrans有用到
        hasICPRecord = 'hasICPRecord'  # 是否有备案
        hasNews = 'hasNews'  # 是否有新闻
        hasRepresentativeContact = 'hasRepresentativeContact'  # 是否存在法人联系方式
        has_annual_report_list = 'has_annual_report_list'  # 年报计算用到
        headOfficeNameId = 'headOfficeNameId'  # 总公司nameId
        heat360 = 'heat360'  # 360热度
        heatBaidu = 'heatBaidu'  # 百度热度
        holderList = 'holderList'  # [`股东列表`](https://gitlab.tangees.com/data-collector/tungee-parser/-/wikis/dimension-check-rules#holderlist)
        holder_list_update_time = 'holder_list_update_time'  # (辅助字段)holderList更新时间
        homepage = 'homepage'  # 企业官网
        homepageAlg = 'homepageAlg'  # 企业官网算法版本
        homepageExists = 'homepageExists'  # 是否存在企业官网:此维度不会入库，只会存在于当算法识别官网时识别不出来任何一个值时，这时候 homepageExists 应该被设置为 False，入库时需要删除该企业的 homepage 字段
        homepageType = 'homepageType'  # 企业官网类型:1: 算法模型解析官网;2: 规则官网（如果为唯一icp且非黄赌毒即认为其为官网）
        homepage_update_time = 'homepage_update_time'  # (辅助字段)Homepage更新时间
        ic_create_time = 'ic_create_time'  # 辅助字段
        ic_failed_no = 'ic_failed_no'  # 辅助字段
        ic_import_update_time = 'ic_import_update_time'  # 辅助字段
        ic_update_source = 'ic_update_source'  # 辅助字段
        ic_update_time = 'ic_update_time'  # (辅助字段)基本信息更新时间
        ic_updating_source = 'ic_updating_source'  # 辅助字段
        icp_update_time = 'icp_update_time'  # 辅助字段
        illegalInfoList = 'illegalInfoList'  # 严重违法信息
        import_update_time = 'import_update_time'  # 导入更新时间
        industry = 'industry'  # 行业(该维度是内部算法生成的，src 字段是具体使用的算法)
        industry_12315 = 'industry_12315'  # 12315行业
        industry_qcc = 'industry_qcc'  # 所属行业
        industry_qidian = 'industry_qidian'  # 所属行业
        industry_tyc = 'industry_tyc'  # 所属行业
        industry_update_time = 'industry_update_time'  # (辅助字段)Industry_12315更新时间
        industry_winhc = 'industry_winhc'  # 所属行业
        investor = 'investor'  # 投资人(独资企业独有)
        iprPledgeList = 'iprPledgeList'  # 知识产权出质信息列表
        isAnomaly = 'isAnomaly'  # 企业当前是否被列入经营异常
        isList = 'isList'  # 是否上市
        is_address_changed = 'is_address_changed'  # 辅助字段
        is_branch_office_list_changed = 'is_branch_office_list_changed'  # 辅助字段
        is_holder_list_changed = 'is_holder_list_changed'  # 辅助字段
        is_top = 'is_top'  # (辅助字段)是否为头部企业
        judicialAssistanceList = 'judicialAssistanceList'  # 司法协助信息
        last_change_branch_office_list_time = 'last_change_branch_office_list_time'  # 分支机构上次变更时间
        last_change_holder_list_time = 'last_change_holder_list_time'  # 股东列表上次变更时间
        last_update_time = 'last_update_time'  # 上次更新时间
        legalRepresentative = 'legalRepresentative'  # [`法人代表`](https://gitlab.tangees.com/data-collector/tungee-parser/-/wikis/dimension-check-rules#legalrepresentative)
        licenseList = 'licenseList'  # 行政许可列表(工商公示)
        licensingDate = 'licensingDate'  # [`发照日期(date_str)`](https://gitlab.tangees.com/data-collector/tungee-parser/-/wikis/dimension-check-rules#licensingdate)
        liquidationInfo = 'liquidationInfo'  # 清算信息
        location = 'location'  # 注册地址坐标信息(目前此数据是使用注册地址信息调用高德接口获得的信息，精度为小数点后6位)
        memberList = 'memberList'  # [`成员列表`](https://gitlab.tangees.com/data-collector/tungee-parser/-/wikis/dimension-check-rules#memberlist)
        micro = 'micro'  # 是否为小微企业
        microPolicySupport = 'microPolicySupport'  # 享受扶持政策信息
        mobileAppEnterpriseType = 'mobileAppEnterpriseType'  # 移动app企业类型(只针对[`移动app相关公司的类型`](https://gitlab.tangees.com/data-collector/tungee-parser/-/wikis/dimension-mapping#%E7%A7%BB%E5%8A%A8app%E4%BC%81%E4%B8%9A%E7%B1%BB%E5%9E%8B%E6%98%A0%E5%B0%84))
        mortgageInfoList = 'mortgageInfoList'  # 动产抵押信息
        name = 'name'  # [`企业全称`](https://gitlab.tangees.com/data-collector/tungee-parser/-/wikis/dimension-check-rules#name)
        nameAlterList = 'nameAlterList'  # 公司名变更列表
        nameId = 'nameId'  # 主键
        nameMd5 = 'nameMd5'  # nameMd5
        news_update_time = 'news_update_time'  # 辅助字段
        officeAddress = 'officeAddress'  # 办公地址
        operBrandList = 'operBrandList'  # 经营品牌列表
        operStatus = 'operStatus'  # [`经营状态`](https://gitlab.tangees.com/data-collector/tungee-parser/-/wikis/dimension-check-rules#operstatus)
        operator = 'operator'  # 经营者(类似于法人代表，只在个体工商户中存在)
        orgCode = 'orgCode'  # [`组织机构代码`](https://gitlab.tangees.com/data-collector/tungee-parser/-/wikis/dimension-check-rules#orgcode)
        orgType = 'orgType'  # [`组织机构类型`](https://gitlab.tangees.com/data-collector/tungee-parser/-/wikis/dimension-check-rules#orgtype)
        paidupCapital = 'paidupCapital'  # 实收资本(统一转换为元，例如：100万欧元->1000000欧元)
        parentCompany = 'parentCompany'  # 母公司
        patentList = 'patentList'  # 专利列表
        patentNo = 'patentNo'  # 专利数
        postCode = 'postCode'  # [`企业邮编`](https://gitlab.tangees.com/data-collector/tungee-parser/-/wikis/dimension-check-rules#postCode)
        principal = 'principal'  # 负责人(分公司或分支机构，国营等企业)
        pripid_failed_no = 'pripid_failed_no'  # 辅助字段
        punishmentList = 'punishmentList'  # 行政处罚信息
        regCapital = 'regCapital'  # [`注册资金`](https://gitlab.tangees.com/data-collector/tungee-parser/-/wikis/dimension-check-rules#regcapital)
        regCity = 'regCity'  # 注册城市(弃用)
        regNumber = 'regNumber'  # [`注册工商号`](https://gitlab.tangees.com/data-collector/tungee-parser/-/wikis/dimension-check-rules#regnumber)
        regProvince = 'regProvince'  # 注册省份(弃用)
        region = 'region'  # [`所在地区`](https://gitlab.tangees.com/data-collector/tungee-parser/-/wikis/dimension-check-rules#region)
        registrationAuthority = 'registrationAuthority'  # [`登记机关`](https://gitlab.tangees.com/data-collector/tungee-parser/-/wikis/dimension-check-rules#registrationauthority)
        repeatNameId = 'repeatNameId'  # 辅助字段
        revokeDate = 'revokeDate'  # 注销日期
        revokeReason = 'revokeReason'  # 注销原因
        shortNameList = 'shortNameList'  # 企业简称列表
        simpleCancellationList = 'simpleCancellationList'  # 简易注销信息
        socialCreditCode = 'socialCreditCode'  # [`统一社会信用代码`](https://gitlab.tangees.com/data-collector/tungee-parser/-/wikis/dimension-check-rules#socialcreditcode)
        source = 'source'  # 供应商数据来源:tyc: 天眼查; qcc: 企查查; bjls: 北京理数
        sourcePubLicenseList = 'sourcePubLicenseList'  # 行政许可列表(地方源)
        szEntUrl = 'szEntUrl'  # 深圳工商Url
        tpIllegalList = 'tpIllegalList'  # 注销信息
        turnoverList = 'turnoverList'  # 营业额列表(统一转换为元，例如：100万美元->1000000美元)
        tyc_annual_list = 'tyc_annual_list'  # 天眼查年报列表
        update_time = 'update_time'  # 更新时间
        weblink = 'weblink'  # [`官网列表`](https://gitlab.tangees.com/data-collector/tungee-parser/-/wikis/dimension-check-rules#weblink)
        homepageReachability = 'homepageReachability'  # 官网是否可达
        homepageVerifyedByAttribute = 'homepageVerifyedByAttribute'  # 通过页面属性校验官网
        homepageVerifyedByICP = 'homepageVerifyedByICP'  # 通过多ICP校验官网

        class AddressField:
            city = 'city'  # (optional) 城市
            district = 'district'  # (optional) 区域：可能是二级市、县或者区
            name = 'name'  # (optional) 映射标准值
            province = 'province'  # (optional) 省份
            subdistrict = 'subdistrict'  #
            value = 'value'  # 原始值

        class AlterationListField:
            alterDate = 'alterDate'  # 变更日期
            alterItem = 'alterItem'  # 变更事项
            auditType = 'auditType'  #
            holderInfo = 'holderInfo'  #
            holderType = 'holderType'  #
            id = 'id'  #
            is_deleted = 'is_deleted'  # 0 未删除 1 删除**天眼查、北京理数有该字段，工商更新不解析**
            name = 'name'  #
            postAlter = 'postAlter'  # 变更后内容
            preAlnfo = 'preAlnfo'  #
            preAlter = 'preAlter'  # 变更前内容
            subscriptionDetail = 'subscriptionDetail'  # 认缴明细

            class SubscriptionDetailField:
                capitalMode = 'capitalMode'  # 认缴出资方式
                amount = 'amount'  #
                date = 'date'  # 认缴出资日期
                publicDate = 'publicDate'  # 公示日期

                class AmountField:
                    value = 'value'  # 实缴金额（元）**若原始值为None，则解析为空字符串**
                    coinType = 'coinType'  # 币种（例如：人民币，美元等），**若币种未知，则不解析此字段**

        class AnomalyListField:
            createAuthority = 'createAuthority'  # 做出决定机关（列入）
            createDate = 'createDate'  # 列入日期
            createReason = 'createReason'  # 列入经营异常名录原因
            is_deleted = 'is_deleted'  # 0 未删除 1 删除**天眼查、北京理数有该字段，工商更新不解析**
            removeAuthority = 'removeAuthority'  # 做出决定机关（移出）
            removeDate = 'removeDate'  # 移出日期
            removeReason = 'removeReason'  # 移出经营异常名录原因

        class BaiduMapAddressField:
            city = 'city'  # (optional) 城市
            district = 'district'  # (optional) 区域：可能是二级市、县或者区
            lat = 'lat'  # 纬度latitude
            lng = 'lng'  # 经度longitude
            province = 'province'  # (optional) 省份
            subdistrict = 'subdistrict'  #
            value = 'value'  # 原始值

        class BranchOfficeListField:
            isBranch = 'isBranch'  # 二次加工维度，用于区分是否是脏数据
            name = 'name'  #
            nameId = 'nameId'  #
            nameIdSource = 'nameIdSource'  # 保留字段
            regSocialCode = 'regSocialCode'  #
            registrationAuthority = 'registrationAuthority'  #

        class BusinessTermField:
            max = 'max'  # (optional) 最大值，时间格式
            min = 'min'  # (optional) 最小值，时间格式

        class CancellationListField:
            creditorsNotice = 'creditorsNotice'  # 债权人公告信息
            liquidationRecord = 'liquidationRecord'  # 清算组备案信息

            class CreditorsNoticeField:
                name = 'name'  # 企业名称
                code = 'code'  # 统一社会信用代码/注册号
                registrationAuthority = 'registrationAuthority'  # 登记机关
                announcementPeriod = 'announcementPeriod'  # 公告期
                announceContent = 'announceContent'  # 公告内容
                creditReportingContactPerson = 'creditReportingContactPerson'  # 债权申报联系人
                creditReportingContact = 'creditReportingContact'  # 债权申报联系电话
                creditReportingAddress = 'creditReportingAddress'  # 债权申报地址
                creditReportingUrl = 'creditReportingUrl'  # 债权申报原链接
                creditReportingImage = 'creditReportingImage'  # 债权申报快照

            class LiquidationRecordField:
                name = 'name'  # 企业名称
                code = 'code'  # 统一社会信用代码/注册号
                registrationAuthority = 'registrationAuthority'  # 登记机关
                liquidationRecordDate = 'liquidationRecordDate'  # 清算组备案日期(date_str)
                liquidationSetupDate = 'liquidationSetupDate'  # 清算组成立日期(date_str)
                cancellationReason = 'cancellationReason'  # 注销原因
                liquidationAddress = 'liquidationAddress'  # 清算组办公地址
                liquidationContact = 'liquidationContact'  # 清算组联系电话
                liquidationChairman = 'liquidationChairman'  # 清算组负责人
                liquidationMember = 'liquidationMember'  # 清算组成员

        class CapitalContributionListField:
            name = 'name'  # 股东名
            payAmount = 'payAmount'  #
            payDetail = 'payDetail'  #
            subscriptionAmount = 'subscriptionAmount'  #
            subscriptionDetail = 'subscriptionDetail'  #

            class PayAmountField:
                value = 'value'  # 总实缴金额（元）**若原始值为None，则解析为空字符串**
                coinType = 'coinType'  # 币种（例如：人民币，美元等），**若币种未知，则不解析此字段**

            class PayDetailField:
                capitalMode = 'capitalMode'  # 实缴出资方式
                amount = 'amount'  #
                date = 'date'  # 实缴出资日期
                publicDate = 'publicDate'  # 公示日期

                class AmountField:
                    value = 'value'  # 实缴金额（元）**若原始值为None，则解析为空字符串**
                    coinType = 'coinType'  # 币种（例如：人民币，美元等），**若币种未知，则不解析此字段**

            class SubscriptionAmountField:
                value = 'value'  # 总认缴金额（元），**若原始值为None，则解析为空字符串**
                coinType = 'coinType'  # 币种（例如：人民币，美元等），**若币种未知，则不解析此字段**

            class SubscriptionDetailField:
                capitalMode = 'capitalMode'  # 认缴出资方式
                amount = 'amount'  #
                date = 'date'  # 认缴出资日期
                publicDate = 'publicDate'  # 公示日期

                class AmountField:
                    value = 'value'  # 实缴金额（元）**若原始值为None，则解析为空字符串**
                    coinType = 'coinType'  # 币种（例如：人民币，美元等），**若币种未知，则不解析此字段**

        class CheckInfoListField:
            authority = 'authority'  # 检查实施机关
            date = 'date'  # 检查日期
            remark = 'remark'  # 备注信息
            result = 'result'  # 检查结果
            type = 'type'  # 检查类型
            is_deleted = 'is_deleted'  # 辅助字段, 0 未删除 1 删除**天眼查、北京理数有该字段，工商更新不解析**

        class CompanyAddressField:
            city = 'city'  #
            district = 'district'  #
            name = 'name'  #
            province = 'province'  #
            value = 'value'  #

        class CreditChinaPubLicenseListField:
            auditType = 'auditType'  # 审核类型
            authority = 'authority'  # 许可机关
            beginDate = 'beginDate'  # 许可有效期自
            content = 'content'  # 内容许可/许可内容
            decideDate = 'decideDate'  # 许可决定日期
            endDate = 'endDate'  # 许可截至日期/许可有效期至
            fileName = 'fileName'  # 行政许可决定文书名称
            id = 'id'  # 行政许可决定书文号/行政许可决定文书号
            legalPerson = 'legalPerson'  # 法定代表人
            licenseId = 'licenseId'  # 许可编号
            licenseLink = 'licenseLink'  # 链接
            licenseName = 'licenseName'  # 许可证书名称
            localCodes = 'localCodes'  # 地方编码
            sourceAuthority = 'sourceAuthority'  # 数据来源单位
            sourceAuthorityId = 'sourceAuthorityId'  # 数据来源单位代码
            status = 'status'  # 当前状态
            updateDate = 'updateDate'  # 数据更新时间/信息更新时间
            validity = 'validity'  # 许可有效期

        class CreditChinaPubPunishmentListField:
            authority = 'authority'  # 处罚机关
            basis = 'basis'  # 处罚依据
            decideDate = 'decideDate'  # 处罚决定日期
            decisionDate = 'decisionDate'  # 保留字段
            endDate = 'endDate'  # 处罚期限
            id = 'id'  # 决定文书号
            is_deleted = 'is_deleted'  # 0 未删除 1 删除**天眼查，工商更新不解析**
            legalPerson = 'legalPerson'  # 法定代表人
            name = 'name'  # 处罚名称
            punishmentFileLink = 'punishmentFileLink'  # 行政处罚决定书链接
            punishmentType = 'punishmentType'  # 违法行为类型
            reason = 'reason'  # 处罚事由
            result = 'result'  # 处罚结果
            type = 'type'  # 处罚类别
            updateDate = 'updateDate'  # 数据更新时间

        class EnNameField:
            enName = 'enName'  # 保留字段
            name = 'name'  # 英文名
            nameMd5 = 'nameMd5'  # name去首尾空格->转英文括号->转大写->转md5
            nameType = 'nameType'  # 保留字段
            source = 'source'  # 名称来源，tyc为天眼查，qcc为企查查

        class EnterpriseContactListField:
            contact = 'contact'  # 联系方式
            contactChannelList = 'contactChannelList'  #
            contactType = 'contactType'  # 联系方式类型
            name = 'name'  # 联系人名
            position = 'position'  # 联系人岗位列表

            class ContactChannelListField:
                source = 'source'  # 此联系人来自于哪个渠道
                contactPageLink = 'contactPageLink'  # 包含联系人的页面链接

        class EnterprisePubLicenseListField:
            alterList = 'alterList'  # 变更列表
            authority = 'authority'  # 许可机关
            auult = 'auult'  #
            basis = 'basis'  #
            beginDate = 'beginDate'  # 起始日期
            content = 'content'  # 许可内容
            decisionDate = 'decisionDate'  #
            endDate = 'endDate'  # 到期日期
            id = 'id'  # 许可文件编号
            legalPerson = 'legalPerson'  #
            name = 'name'  # 许可文件名称
            reason = 'reason'  #
            result = 'result'  #
            status = 'status'  # 状态
            type = 'type'  #

            class AlterListField:
                alterItem = 'alterItem'  # 变更事项
                preAlter = 'preAlter'  # 变更前
                postAlter = 'postAlter'  # 变更后
                alterDate = 'alterDate'  # 变更日期

        class EquityAlterListField:
            alterDate = 'alterDate'  # 股权变更日期
            name = 'name'  # 股东名
            postAlter = 'postAlter'  # 变更后股权比例（原始值，0~100）
            preAlter = 'preAlter'  # 变更前股权比例（原始值，0~100）
            publicDate = 'publicDate'  # 公示日期

        class EquityPledgeListField:
            date = 'date'  # 股权出质设立登记日期
            equityAmount = 'equityAmount'  # 出质股权数额
            id = 'id'  # 登记编号
            pledgee = 'pledgee'  # 质权人
            pledgeeId = 'pledgeeId'  # 质权人证照/证件号码
            pledgor = 'pledgor'  # 出质人
            pledgorId = 'pledgorId'  # 出质人证照/证件号码
            publicationDate = 'publicationDate'  # 公示日期
            revokeDate = 'revokeDate'  # 注销日期（详情页）
            revokeReason = 'revokeReason'  # 注销原因（详情页）
            status = 'status'  # 状态
            is_deleted = 'is_deleted'  # (辅助字段)未删除 1 删除**天眼查、北京理数有该字段，工商更新不解析**

        class ExtractBranchOfficeListField:
            name = 'name'  #
            nameId = 'nameId'  #

        class FinancingListField:
            amount = 'amount'  # 融资金额，可能为 `未透露` 等
            investorList = 'investorList'  # (optional) 投资方列表，可以为公司或者个人
            round = 'round'  # 第几轮，可能为 `战略投资`，`并购` 等
            time = 'time'  # (optional) 融资时间

        class HasHisBdPromotionField:
            date = 'date'  #
            state = 'state'  #

        class Heat360Field:
            citedNo = 'citedNo'  # 热度收录量
            citedTime = 'citedTime'  # 爬虫爬取收录量的日期 XXXX-XX-XX

        class HeatBaiduField:
            citedNo = 'citedNo'  # 热度收录量
            citedTime = 'citedTime'  # 爬虫爬取收录量的日期 XXXX-XX-XX

        class HolderListField:
            displayIdNo = 'displayIdNo'  # 是否显示证件号码，若为False，显示为`非公示项`
            displayIdType = 'displayIdType'  # 是否显示证件类型，若为False，显示为`非公示项`
            forms = 'forms'  # 出资方式 (个人独资企业才有的维度)
            holderInfo = 'holderInfo'  # 股东详情页
            holderType = 'holderType'  # 股东类型
            identifacationNo = 'identifacationNo'  # 证件号码，为身份证（人）或注册号（企业）
            identificationType = 'identificationType'  # 证件类型
            name = 'name'  # 股东名，可以是人名或者企业名
            nameId = 'nameId'  # 保留字段
            nameIdSource = 'nameIdSource'  # 保留字段
            payDetail = 'payDetail'  # 实缴明细
            subscriptionDetail = 'subscriptionDetail'  # 认缴明细

            class HolderInfoField:
                name = 'name'  # 股东名称
                payAmount = 'payAmount'  # 实缴出资额（元），由 payDetail 中的 amount 累加得到
                payAmountOrigin = 'payAmountOrigin'  # 页面原始值
                payAmountOriginCoinType = 'payAmountOriginCoinType'  # optional, 实缴出资额币种, 目前只有广东旧工商源才需要解析此字段
                subscriptionAmount = 'subscriptionAmount'  # 认缴额（元），由 subscriptionDetail 中的 amount 累加得到
                subscriptionAmountOrigin = 'subscriptionAmountOrigin'  # 页面原始值
                subscriptionAmountOriginCoinType = 'subscriptionAmountOriginCoinType'  # optional, 认缴出资额币种, 目前只有广东旧工商源才需要解析此字段

            class PayDetailField:
                capitalMode = 'capitalMode'  # 实缴出资方式
                amount = 'amount'  # 实缴出资金额
                date = 'date'  # 实缴出资日期
                coinType = 'coinType'  # 币种（例如：人民币，美元等）

            class SubscriptionDetailField:
                capitalMode = 'capitalMode'  # 认缴出资方式
                amount = 'amount'  # 认缴出资额（元）
                date = 'date'  # 认缴出资日期
                coinType = 'coinType'  # 币种（例如：人民币，美元等）

        class IllegalInfoListField:
            createAuthority = 'createAuthority'  # 做出决定机关（列入）
            createDate = 'createDate'  # 列入日期
            createReason = 'createReason'  # 列入原因
            fact = 'fact'  # 违法事实
            is_deleted = 'is_deleted'  # 0 未删除 1 删除**天眼查、北京理数有该字段，工商更新不解析**
            removeAuthority = 'removeAuthority'  # 做出决定机关（移除）
            removeDate = 'removeDate'  # 移除日期
            removeReason = 'removeReason'  # 移除原因
            type = 'type'  # 类别

        class IndustryField:
            firstIndustry = 'firstIndustry'  #
            firstIndustryRate = 'firstIndustryRate'  #
            src = 'src'  # 保留字段

        class Industry_12315Field:
            firstIndustry = 'firstIndustry'  #
            firstIndustryCode = 'firstIndustryCode'  #
            secondIndustry = 'secondIndustry'  #
            secondIndustryCode = 'secondIndustryCode'  #
            thirdIndustry = 'thirdIndustry'  #
            thirdIndustryCode = 'thirdIndustryCode'  #
            fourthIndustry = 'fourthIndustry'  #
            fourthIndustryCode = 'fourthIndustryCode'  #
            originCode = 'originCode'  # 12315爬回来的原始编码
            originName = 'originName'  # 保留

        class Industry_qccField:
            firstIndustry = 'firstIndustry'  #
            fourthIndustry = 'fourthIndustry'  #
            secondIndustry = 'secondIndustry'  #
            thirdIndustry = 'thirdIndustry'  #
            value = 'value'  #

        class Industry_qidianField:
            firstIndustry = 'firstIndustry'  # 一级行业名称，对应cate_first
            secondIndustry = 'secondIndustry'  #
            thirdIndustry = 'thirdIndustry'  #
            fourthIndustry = 'fourthIndustry'  #
            originCode = 'originCode'  #

        class Industry_tycField:
            firstIndustry = 'firstIndustry'  # 一级行业名称，对应cate_first
            fourthIndustry = 'fourthIndustry'  # 四级行业名称，暂无对应，为空
            originCode = 'originCode'  # 行业分类代码，对应category_code
            secondIndustry = 'secondIndustry'  # 二级行业名称，对应cate_second
            thirdIndustry = 'thirdIndustry'  # 三级行业名称，对应cate_third

        class Industry_winhcField:
            firstIndustry = 'firstIndustry'  # 一级行业名称，对应cate_first
            fourthIndustry = 'fourthIndustry'  # 四级行业名称，暂无对应，为空
            secondIndustry = 'secondIndustry'  # 二级行业名称，对应cate_second
            thirdIndustry = 'thirdIndustry'  # 三级行业名称，对应cate_third
            originCode = 'originCode'  #

        class IprPledgeListField:
            iprName = 'iprName'  # 名称
            iprPledgeAlterationList = 'iprPledgeAlterationList'  #
            iprPledgePeriod = 'iprPledgePeriod'  # 质权登记期限
            iprPledgePublicDate = 'iprPledgePublicDate'  # 公示日期
            iprPledgeRevokeDate = 'iprPledgeRevokeDate'  # 注销日期
            iprPledgeRevokeReason = 'iprPledgeRevokeReason'  # 注销原因
            iprPledgeeName = 'iprPledgeeName'  # 质权人名称
            iprPledgorName = 'iprPledgorName'  # 出质人名称
            iprRegisterNum = 'iprRegisterNum'  # 知识产权登记号
            iprStatus = 'iprStatus'  # 状态
            iprType = 'iprType'  # 种类

            class IprPledgeAlterationListField:
                alterItem = 'alterItem'  # 变更事项
                preAlter = 'preAlter'  # 变更前内容
                postAlter = 'postAlter'  # 变更后内容
                alter_date = 'alter_date'  # 变更日期

        class JudicialAssistanceListField:
            court = 'court'  # 执行法院
            defaulterName = 'defaulterName'  # 被执行人
            equityAmount = 'equityAmount'  # 股权数额
            equityChangeInfo = 'equityChangeInfo'  # 股权变更信息
            freezeInfo = 'freezeInfo'  # 冻结情况
            freezeKeepInfo = 'freezeKeepInfo'  # 续行冻结情况
            freezeRemoveInfo = 'freezeRemoveInfo'  # 解冻情况
            invalidationInfo = 'invalidationInfo'  # 失效信息
            jaDeleted = 'jaDeleted'  # 司法协助信息删除状态
            noticeNumber = 'noticeNumber'  # 执行通知书文号
            status = 'status'  # 类型|状态

            class EquityChangeInfoField:
                court = 'court'  # 执行法院
                case = 'case'  # 执行事项
                decisionNumber = 'decisionNumber'  # 执行裁定书文号
                noticeNumber = 'noticeNumber'  # 执行通知书文号
                defaulterName = 'defaulterName'  # 被执行人
                defaulterEquityAmount = 'defaulterEquityAmount'  # 被执行人持有股权数额
                defaulterIdType = 'defaulterIdType'  # 被执行人证照种类
                defaulterIdNumber = 'defaulterIdNumber'  # 被执行人证照号码
                assigneeName = 'assigneeName'  # 受让人
                executionDate = 'executionDate'  # 协助执行日期
                assigneeIdType = 'assigneeIdType'  # 受让人证照种类
                assigneeIdNumber = 'assigneeIdNumber'  # 受让人证照号码

            class FreezeInfoField:
                court = 'court'  # 执行法院
                case = 'case'  # 执行事项
                decisionNumber = 'decisionNumber'  # 执行裁定书文号
                noticeNumber = 'noticeNumber'  # 执行通知书文号
                defaulterName = 'defaulterName'  # 被执行人
                defaulterEquityAmount = 'defaulterEquityAmount'  # 被执行人持有股权、其它投资权益的数额
                defaulterIdType = 'defaulterIdType'  # 被执行人证照种类
                defaulterIdNumber = 'defaulterIdNumber'  # 被执行人证照号码
                beginDate = 'beginDate'  # 冻结期限自
                endDate = 'endDate'  # 冻结期限至
                term = 'term'  # 冻结期限
                publicationDate = 'publicationDate'  # 公示日期

            class FreezeKeepInfoField:
                court = 'court'  # 执行法院
                case = 'case'  # 执行事项
                decisionNumber = 'decisionNumber'  # 执行裁定书文号
                noticeNumber = 'noticeNumber'  # 执行通知书文号
                defaulterName = 'defaulterName'  # 被执行人
                defaulterEquityAmount = 'defaulterEquityAmount'  # 被执行人持有股权、其它投资权益的数额
                defaulterIdType = 'defaulterIdType'  # 被执行人证照种类
                defaulterIdNumber = 'defaulterIdNumber'  # 被执行人证照号码
                beginDate = 'beginDate'  # 冻结期限自(date_str)
                endDate = 'endDate'  # 冻结期限至(date_str)
                term = 'term'  # 冻结期限
                publicationDate = 'publicationDate'  # 公示日期(date_str)

            class FreezeRemoveInfoField:
                court = 'court'  # 执行法院
                case = 'case'  # 执行事项
                decisionNumber = 'decisionNumber'  # 执行裁定书文号
                noticeNumber = 'noticeNumber'  # 执行通知书文号
                defaulterName = 'defaulterName'  # 被执行人
                defaulterEquityAmount = 'defaulterEquityAmount'  # 被执行人持有股权、其它投资权益的数额
                defaulterIdType = 'defaulterIdType'  # 被执行人证照种类
                defaulterIdNumber = 'defaulterIdNumber'  # 被执行人证照号码
                freezeRemoveDate = 'freezeRemoveDate'  # 解除冻结日期
                publicationDate = 'publicationDate'  # 公示日期
                invalidationReason = 'invalidationReason'  # 失效原因
                invalidationDate = 'invalidationDate'  # 失效日期

            class InvalidationInfoField:
                reason = 'reason'  # 失效原因
                date = 'date'  # 失效日期

        class LicenseListField:
            authority = 'authority'  # 许可机关
            beginDate = 'beginDate'  # 起始日期
            content = 'content'  # 范围
            endDate = 'endDate'  # 到期日期
            grade = 'grade'  # 等级，**天眼查独有，工商更新不解析**
            id = 'id'  # 许可文件编号
            issueDate = 'issueDate'  # 发证日期，**天眼查独有，工商更新不解析**
            name = 'name'  # 许可文件名称
            status = 'status'  # 证书状态，**天眼查独有，工商更新不解析**
            type = 'type'  # 类型，**天眼查独有，工商更新不解析**

        class LiquidationInfoField:
            manager = 'manager'  # 清算组负责人
            memberList = 'memberList'  # 清算成员

        class LocationField:
            lat = 'lat'  # 纬度latitude
            lng = 'lng'  # 经度longitude

        class MemberListField:
            name = 'name'  # 成员名
            position = 'position'  # (optional) 职位

        class MicroPolicySupportField:
            authority = 'authority'  # 实施扶持政策部门
            basis = 'basis'  # 享受扶持政策依据
            content = 'content'  # 享受扶持政策内容
            date = 'date'  # 享受扶持政策日期(类型为日期字符串)
            value = 'value'  # 享受扶持政策金额

        class MortgageInfoListField:
            alterationList = 'alterationList'  # 变更信息
            amount = 'amount'  # 被担保债权数额
            authority = 'authority'  # 登记机关
            date = 'date'  # 登记日期
            guaranteedCreditorInfo = 'guaranteedCreditorInfo'  # 被担保主债权信息
            id = 'id'  # 登记编号
            mortgageeList = 'mortgageeList'  # 抵押权人信息
            pawnList = 'pawnList'  # 抵押物信息
            publicationDate = 'publicationDate'  # 公示日期
            revokeInfo = 'revokeInfo'  # 注销信息

            class AlterationListField:
                date = 'date'  # 变更日期
                content = 'content'  # 变更内容

            class GuaranteedCreditorInfoField:
                type = 'type'  # 种类
                scope = 'scope'  # 担保的范围
                amount = 'amount'  # 数额
                coinType = 'coinType'  # 币种
                term = 'term'  # 债务人履行债务的期限
                remark = 'remark'  # 备注

            class MortgageeListField:
                name = 'name'  # 抵押权人名称
                identificationType = 'identificationType'  # 证件类型
                identifacationNo = 'identifacationNo'  # 证件号码
                address = 'address'  # 住所地

            class PawnListField:
                name = 'name'  # 抵押物名称
                owner = 'owner'  # 所有权或使用权归属
                detail = 'detail'  # 数量、质量、状况、所在地等情况
                remark = 'remark'  # 备注

            class RevokeInfoField:
                date = 'date'  # 注销日期
                reason = 'reason'  # 注销原因

        class NameAlterListField:
            alterDate = 'alterDate'  # 变更日期(date_str)
            alterSource = 'alterSource'  # 变更来源：入库、合并主体
            postAlter = 'postAlter'  # 变更后企业名（加密）
            preAlter = 'preAlter'  # 变更前企业名（加密）
            preAlterMd5 = 'preAlterMd5'  # 保留字段

        class OfficeAddressField:
            address = 'address'  # 地址
            source = 'source'  # 来源

        class OperStatusField:
            name = 'name'  # 映射标准值
            value = 'value'  # 原始值

        class PaidupCapitalField:
            coinType = 'coinType'  # 币种
            value = 'value'  # 资本值

        class ParentCompanyField:
            address = 'address'  # 母公司地址
            name = 'name'  # 母公司名称
            regNo = 'regNo'  # 母公司注册号

        class PatentListField:
            address = 'address'  # 地址
            agent = 'agent'  # 代理机构（公司）
            agentorList = 'agentorList'  # 代理人列表，如：[`小米`, `小明`]
            aplydate = 'aplydate'  # 申请日期
            inventorList = 'inventorList'  # 发明人列表，如：[`小米`, `小明`]
            mainTypeNumList = 'mainTypeNumList'  # 专利主分类号
            name = 'name'  # 专利名
            number = 'number'  # 专利号
            type = 'type'  # 专利类型，如：实用新型
            typeNumList = 'typeNumList'  # 专利分类号

            class AddressField:
                value = 'value'  # 地址原始值
                name = 'name'  # 地址映射值

        class PunishmentListField:
            authority = 'authority'  # 决定机关名称
            content = 'content'  # 行政处罚内容
            decisionDate = 'decisionDate'  # 处罚决定日期
            desc = 'desc'  # 描述, **天眼查独有，工商更新不解析**
            id = 'id'  # 决定文书号
            is_deleted = 'is_deleted'  # 0 未删除 1 删除**天眼查、北京理数有该字段，工商更新不解析**
            publishDate = 'publishDate'  # 公示日期
            punishmentFileLink = 'punishmentFileLink'  # 行政处罚决定书链接
            type = 'type'  # 违法行为类型

        class RegCapitalField:
            coinType = 'coinType'  # 币种（例如：人民币，美元等） **若币种未知，则不解析此字段**
            origin = 'origin'  # 页面原始值
            unit = 'unit'  # (optional)单位，必须为`元`
            value = 'value'  # 注册金额，可能为字符串`无`

        class RegionField:
            city = 'city'  # (optional) 城市
            district = 'district'  # (optional) 区域：可能是二级市、县或者区
            name = 'name'  # (optional) 映射标准值
            province = 'province'  # (optional) 省份
            value = 'value'  # 原始值

        class SimpleCancellationListField:
            code = 'code'  # 统一社会信用代码
            is_deleted = 'is_deleted'  # 0 未删除 1 删除**天眼查、北京理数有该字段，工商更新不解析**
            name = 'name'  # 企业名称
            publicRange = 'publicRange'  # 公告期
            registrationAuthority = 'registrationAuthority'  # 登记机关
            simpleCancellationResult = 'simpleCancellationResult'  # 简易注销结果
            objectionInformation = 'objectionInformation'  # 异议信息
            title = 'title'  # 公告名称

            class ObjectionInformationField:
                objectionApplicant = 'objectionApplicant'  # 异议申请人
                objectionContent = 'objectionContent'  # 异议内容
                objectionTime = 'objectionTime'  # 异议时间

            class SimpleCancellationResultField:
                result = 'result'  # 简易注销结果
                date = 'date'  # 核准日期(date_str)

        class SourcePubLicenseListField:
            id = 'id'  # 行政许可决定书文号/行政许可决定文书号
            fileName = 'fileName'  # 行政许可决定文书名称
            licenseName = 'licenseName'  # 许可证书名称
            licenseId = 'licenseId'  # 许可编号
            legalPerson = 'legalPerson'  # 法定代表人
            content = 'content'  # 内容许可/许可内容
            beginDate = 'beginDate'  # 许可有效期自
            decideDate = 'decideDate'  # 许可决定日期
            endDate = 'endDate'  # 许可截至日期/许可有效期至
            status = 'status'  # 当前状态
            authority = 'authority'  # 许可机关
            source = 'source'  # 源
            authorityType = 'authorityType'  # 许可类别

        class TpIllegalListField:
            withdrawDate = 'withdrawDate'  # 撤销日期
            withdrawReason = 'withdrawReason'  # 撤销原因
            logoutDate = 'logoutDate'  # 注销日期
            logoutReason = 'logoutReason'  # 注销原因
            revocationDate = 'revocationDate'  # 吊销日期
            revocationReason = 'revocationReason'  # 吊销原因
            registeredCapitalOrigin = 'registeredCapitalOrigin'  # 注册资金单位统一化：注册资金工商原始值
            registeredCapital = 'registeredCapital'  # 注册资金单位统一化：注册资金金额、注册资金类型
            updateTime = 'updateTime'  # 更新时间
            setupTime = 'setupTime'  # 创建时间

            class RegisteredCapitalField:
                value = 'value'  # 注册金额，可能为字符串`无`
                coinType = 'coinType'  # 币种（例如：人民币，美元等）
                unit = 'unit'  # (optional)单位，必须为`元`

            class RegisteredCapitalOriginField:
                value = 'value'  # 注册金额，可能为字符串`无`
                coinType = 'coinType'  # 币种（例如：人民币，美元等）
                unit = 'unit'  # (optional)单位，必须为`元`

        class TurnoverListField:
            coinType = 'coinType'  # 币种
            max = 'max'  # 最大值
            min = 'min'  # 最小值
            period = 'period'  # 周期只能为 `年/月/日/季度` 中的一种

    SPARK_SCHEMA = {
        "fields": [
            {
                "metadata": {

                },
                "name": "sourcePubLicenseList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "id",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "fileName",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "licenseName",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "licenseId",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "legalPerson",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "content",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "beginDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "decideDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "endDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "status",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "authority",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "source",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "authorityType",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "tpIllegalList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "withdrawDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "withdrawReason",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "logoutDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "logoutReason",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "revocationDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "revocationReason",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "registeredCapitalOrigin",
                                "nullable": True,
                                "type": {
                                    "fields": [
                                        {
                                            "metadata": {

                                            },
                                            "name": "value",
                                            "nullable": True,
                                            "type": "double"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "coinType",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "unit",
                                            "nullable": True,
                                            "type": "string"
                                        }
                                    ],
                                    "type": "struct"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "registeredCapital",
                                "nullable": True,
                                "type": {
                                    "fields": [
                                        {
                                            "metadata": {

                                            },
                                            "name": "value",
                                            "nullable": True,
                                            "type": "double"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "coinType",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "unit",
                                            "nullable": True,
                                            "type": "string"
                                        }
                                    ],
                                    "type": "struct"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "updateTime",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "setupTime",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "BdAuthenticationCode",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "BdAuthenticationTypeList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "BdCooperationTime",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "BdSiteName",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "BdWeblink",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "_id",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "_searchKey",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "_searchType",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "_spiderIcComplete",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {

                },
                "name": "address",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "city",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "district",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "name",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "province",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "subdistrict",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "value",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "agentSystemList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "alterationList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "alterDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "alterItem",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "is_deleted",
                                "nullable": True,
                                "type": "long"
                            },
                            {
                                "metadata": {

                                },
                                "name": "postAlter",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "preAlter",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "alteration_list_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "anomalyList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "createAuthority",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "createDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "createReason",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "is_deleted",
                                "nullable": True,
                                "type": "long"
                            },
                            {
                                "metadata": {

                                },
                                "name": "removeAuthority",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "removeDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "removeReason",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "anomalySource",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "anomaly_list_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "app_update_time",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "baiduMapAddress",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "city",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "district",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "lat",
                            "nullable": True,
                            "type": "double"
                        },
                        {
                            "metadata": {

                            },
                            "name": "lng",
                            "nullable": True,
                            "type": "double"
                        },
                        {
                            "metadata": {

                            },
                            "name": "province",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "subdistrict",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "value",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "branchOfficeList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "isBranch",
                                "nullable": True,
                                "type": "boolean"
                            },
                            {
                                "metadata": {

                                },
                                "name": "name",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameId",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameIdSource",
                                "nullable": True,
                                "type": {
                                    "fields": [

                                    ],
                                    "type": "struct"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "regSocialCode",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "registrationAuthority",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "business",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "businessTags",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "businessTerm",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "max",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "min",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "businessType",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "cancellationList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "creditorsNotice",
                                "nullable": True,
                                "type": {
                                    "fields": [
                                        {
                                            "metadata": {

                                            },
                                            "name": "name",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "code",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "registrationAuthority",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "announcementPeriod",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "announceContent",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "creditReportingContactPerson",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "creditReportingContact",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "creditReportingAddress",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "creditReportingUrl",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "creditReportingImage",
                                            "nullable": True,
                                            "type": "string"
                                        }
                                    ],
                                    "type": "struct"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "liquidationRecord",
                                "nullable": True,
                                "type": {
                                    "fields": [
                                        {
                                            "metadata": {

                                            },
                                            "name": "name",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "code",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "registrationAuthority",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "liquidationRecordDate",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "liquidationSetupDate",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "cancellationReason",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "liquidationAddress",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "liquidationContact",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "liquidationChairman",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "liquidationMember",
                                            "nullable": True,
                                            "type": "string"
                                        }
                                    ],
                                    "type": "struct"
                                }
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "capitalContributionList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "name",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "payAmount",
                                "nullable": True,
                                "type": {
                                    "fields": [
                                        {
                                            "metadata": {

                                            },
                                            "name": "value",
                                            "nullable": True,
                                            "type": "double"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "coinType",
                                            "nullable": True,
                                            "type": "string"
                                        }
                                    ],
                                    "type": "struct"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "payDetail",
                                "nullable": True,
                                "type": {
                                    "containsNull": True,
                                    "elementType": {
                                        "fields": [
                                            {
                                                "metadata": {

                                                },
                                                "name": "capitalMode",
                                                "nullable": True,
                                                "type": "string"
                                            },
                                            {
                                                "metadata": {

                                                },
                                                "name": "amount",
                                                "nullable": True,
                                                "type": {
                                                    "fields": [
                                                        {
                                                            "metadata": {

                                                            },
                                                            "name": "value",
                                                            "nullable": True,
                                                            "type": "double"
                                                        },
                                                        {
                                                            "metadata": {

                                                            },
                                                            "name": "coinType",
                                                            "nullable": True,
                                                            "type": "string"
                                                        }
                                                    ],
                                                    "type": "struct"
                                                }
                                            },
                                            {
                                                "metadata": {

                                                },
                                                "name": "date",
                                                "nullable": True,
                                                "type": "string"
                                            },
                                            {
                                                "metadata": {

                                                },
                                                "name": "publicDate",
                                                "nullable": True,
                                                "type": "string"
                                            }
                                        ],
                                        "type": "struct"
                                    },
                                    "type": "array"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "subscriptionAmount",
                                "nullable": True,
                                "type": {
                                    "fields": [
                                        {
                                            "metadata": {

                                            },
                                            "name": "value",
                                            "nullable": True,
                                            "type": "double"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "coinType",
                                            "nullable": True,
                                            "type": "string"
                                        }
                                    ],
                                    "type": "struct"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "subscriptionDetail",
                                "nullable": True,
                                "type": {
                                    "containsNull": True,
                                    "elementType": {
                                        "fields": [
                                            {
                                                "metadata": {

                                                },
                                                "name": "capitalMode",
                                                "nullable": True,
                                                "type": "string"
                                            },
                                            {
                                                "metadata": {

                                                },
                                                "name": "amount",
                                                "nullable": True,
                                                "type": {
                                                    "fields": [
                                                        {
                                                            "metadata": {

                                                            },
                                                            "name": "value",
                                                            "nullable": True,
                                                            "type": "double"
                                                        },
                                                        {
                                                            "metadata": {

                                                            },
                                                            "name": "coinType",
                                                            "nullable": True,
                                                            "type": "string"
                                                        }
                                                    ],
                                                    "type": "struct"
                                                }
                                            },
                                            {
                                                "metadata": {

                                                },
                                                "name": "date",
                                                "nullable": True,
                                                "type": "string"
                                            },
                                            {
                                                "metadata": {

                                                },
                                                "name": "publicDate",
                                                "nullable": True,
                                                "type": "string"
                                            }
                                        ],
                                        "type": "struct"
                                    },
                                    "type": "array"
                                }
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "checkInfoList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "authority",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "date",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "remark",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "result",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "type",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "is_deleted",
                                "nullable": True,
                                "type": "long"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "cid",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "companyAddress",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "city",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "district",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "name",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "province",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "value",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "composition",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "crawl_source",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "create_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "creditChinaPubLicenseList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "auditType",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "authority",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "beginDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "content",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "decideDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "endDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "fileName",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "id",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "legalPerson",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "licenseId",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "licenseLink",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "licenseName",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "localCodes",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "sourceAuthority",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "sourceAuthorityId",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "status",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "updateDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "validity",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "creditChinaPubPunishmentList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "authority",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "basis",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "decideDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "decisionDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "endDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "id",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "is_deleted",
                                "nullable": True,
                                "type": "long"
                            },
                            {
                                "metadata": {

                                },
                                "name": "legalPerson",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "name",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "punishmentFileLink",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "punishmentType",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "reason",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "result",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "type",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "updateDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "penaltyAmount",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "confiscationAmount",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "revocationCertificate",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "deactivateDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "deactivateReason",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "desc",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "devSystemList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "enName",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "enName",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "name",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameMd5",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameType",
                                "nullable": True,
                                "type": "long"
                            },
                            {
                                "metadata": {

                                },
                                "name": "source",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "enterpriseChannelList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "enterpriseContactList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "contact",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "contactChannelList",
                                "nullable": True,
                                "type": {
                                    "containsNull": True,
                                    "elementType": {
                                        "fields": [
                                            {
                                                "metadata": {

                                                },
                                                "name": "source",
                                                "nullable": True,
                                                "type": "string"
                                            },
                                            {
                                                "metadata": {

                                                },
                                                "name": "contactPageLink",
                                                "nullable": True,
                                                "type": "string"
                                            }
                                        ],
                                        "type": "struct"
                                    },
                                    "type": "array"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "contactType",
                                "nullable": True,
                                "type": "long"
                            },
                            {
                                "metadata": {

                                },
                                "name": "name",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "position",
                                "nullable": True,
                                "type": {
                                    "containsNull": True,
                                    "elementType": "string",
                                    "type": "array"
                                }
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "enterpriseExists",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {

                },
                "name": "enterprisePubLicenseList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "alterList",
                                "nullable": True,
                                "type": {
                                    "containsNull": True,
                                    "elementType": {
                                        "fields": [
                                            {
                                                "metadata": {

                                                },
                                                "name": "alterItem",
                                                "nullable": True,
                                                "type": "string"
                                            },
                                            {
                                                "metadata": {

                                                },
                                                "name": "preAlter",
                                                "nullable": True,
                                                "type": "string"
                                            },
                                            {
                                                "metadata": {

                                                },
                                                "name": "postAlter",
                                                "nullable": True,
                                                "type": "string"
                                            },
                                            {
                                                "metadata": {

                                                },
                                                "name": "alterDate",
                                                "nullable": True,
                                                "type": "string"
                                            }
                                        ],
                                        "type": "struct"
                                    },
                                    "type": "array"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "authority",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "auult",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "basis",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "beginDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "content",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "decisionDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "endDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "id",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "legalPerson",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "name",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "reason",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "result",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "status",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "type",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "cancelDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "cancelReason",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "revokeDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "revokeReason",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "invalidDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "invalidReason",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "enterpriseType",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "entityAlgType",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {

                },
                "name": "equityAlterList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "alterDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "name",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "postAlter",
                                "nullable": True,
                                "type": "double"
                            },
                            {
                                "metadata": {

                                },
                                "name": "preAlter",
                                "nullable": True,
                                "type": "double"
                            },
                            {
                                "metadata": {

                                },
                                "name": "publicDate",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "equityPledgeList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "date",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "equityAmount",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "id",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "pledgee",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "pledgeeId",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "pledgor",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "pledgorId",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "publicationDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "revokeDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "revokeReason",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "status",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "is_deleted",
                                "nullable": True,
                                "type": "long"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "executivePartner",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "extractBranchOfficeList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "name",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameId",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "financingList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "amount",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "investorList",
                                "nullable": True,
                                "type": {
                                    "containsNull": True,
                                    "elementType": "string",
                                    "type": "array"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "round",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "time",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "formerNames",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "foundTime",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "gsxtEntType",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "gsxtNodeNum",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "gsxtPripId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "hasBdPromotion",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {

                },
                "name": "hasDesc",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {

                },
                "name": "hasHisBdPromotion",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "date",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "state",
                                "nullable": True,
                                "type": "boolean"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "hasICPRecord",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {

                },
                "name": "hasNews",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {

                },
                "name": "hasRepresentativeContact",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {

                },
                "name": "has_annual_report_list",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "long",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "headOfficeNameId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "heat360",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "citedNo",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "citedTime",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "heatBaidu",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "citedNo",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "citedTime",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "holderList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "displayIdNo",
                                "nullable": True,
                                "type": "boolean"
                            },
                            {
                                "metadata": {

                                },
                                "name": "displayIdType",
                                "nullable": True,
                                "type": "boolean"
                            },
                            {
                                "metadata": {

                                },
                                "name": "forms",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "holderInfo",
                                "nullable": True,
                                "type": {
                                    "fields": [
                                        {
                                            "metadata": {

                                            },
                                            "name": "name",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "payAmount",
                                            "nullable": True,
                                            "type": "double"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "payAmountOrigin",
                                            "nullable": True,
                                            "type": "double"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "payAmountOriginCoinType",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "subscriptionAmount",
                                            "nullable": True,
                                            "type": "double"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "subscriptionAmountOrigin",
                                            "nullable": True,
                                            "type": "double"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "subscriptionAmountOriginCoinType",
                                            "nullable": True,
                                            "type": "string"
                                        }
                                    ],
                                    "type": "struct"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "holderType",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "country",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "identifacationNo",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "identificationType",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "name",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameId",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "nameIdSource",
                                "nullable": True,
                                "type": {
                                    "fields": [

                                    ],
                                    "type": "struct"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "payDetail",
                                "nullable": True,
                                "type": {
                                    "containsNull": True,
                                    "elementType": {
                                        "fields": [
                                            {
                                                "metadata": {

                                                },
                                                "name": "capitalMode",
                                                "nullable": True,
                                                "type": "string"
                                            },
                                            {
                                                "metadata": {

                                                },
                                                "name": "amount",
                                                "nullable": True,
                                                "type": "double"
                                            },
                                            {
                                                "metadata": {

                                                },
                                                "name": "date",
                                                "nullable": True,
                                                "type": "string"
                                            },
                                            {
                                                "metadata": {

                                                },
                                                "name": "coinType",
                                                "nullable": True,
                                                "type": "string"
                                            }
                                        ],
                                        "type": "struct"
                                    },
                                    "type": "array"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "subscriptionDetail",
                                "nullable": True,
                                "type": {
                                    "containsNull": True,
                                    "elementType": {
                                        "fields": [
                                            {
                                                "metadata": {

                                                },
                                                "name": "capitalMode",
                                                "nullable": True,
                                                "type": "string"
                                            },
                                            {
                                                "metadata": {

                                                },
                                                "name": "amount",
                                                "nullable": True,
                                                "type": "double"
                                            },
                                            {
                                                "metadata": {

                                                },
                                                "name": "date",
                                                "nullable": True,
                                                "type": "string"
                                            },
                                            {
                                                "metadata": {

                                                },
                                                "name": "coinType",
                                                "nullable": True,
                                                "type": "string"
                                            }
                                        ],
                                        "type": "struct"
                                    },
                                    "type": "array"
                                }
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "holder_list_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "homepage",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "homepageAlg",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "homepageExists",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {

                },
                "name": "homepageType",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {

                },
                "name": "homepage_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "homepageReachability",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {

                },
                "name": "homepageSource",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "homepageConfidence",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {

                },
                "name": "homepageVerifyedByAttribute",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {

                },
                "name": "homepageVerifyedByICP",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {

                },
                "name": "ic_create_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "ic_failed_no",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {

                },
                "name": "pripid_failed_no",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {

                },
                "name": "ic_import_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "ic_update_source",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "ic_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "ic_updating_source",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "icp_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "illegalInfoList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "createAuthority",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "createDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "createReason",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "fact",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "is_deleted",
                                "nullable": True,
                                "type": "long"
                            },
                            {
                                "metadata": {

                                },
                                "name": "removeAuthority",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "removeDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "removeReason",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "type",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "import_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "industry",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "firstIndustry",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "firstIndustryRate",
                            "nullable": True,
                            "type": "double"
                        },
                        {
                            "metadata": {

                            },
                            "name": "secondIndustries",
                            "nullable": True,
                            "type": {
                                "containsNull": True,
                                "elementType": {
                                    "fields": [
                                        {
                                            "metadata": {

                                            },
                                            "name": "secondIndustry",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "secondIndustryRate",
                                            "nullable": True,
                                            "type": "double"
                                        }
                                    ],
                                    "type": "struct"
                                },
                                "type": "array"
                            }
                        },
                        {
                            "metadata": {

                            },
                            "name": "src",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "industry_12315",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "firstIndustry",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "firstIndustryCode",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "secondIndustry",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "secondIndustryCode",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "thirdIndustry",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "thirdIndustryCode",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "fourthIndustry",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "fourthIndustryCode",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "originCode",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "originName",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "industry_qcc",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "firstIndustry",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "fourthIndustry",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "secondIndustry",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "thirdIndustry",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "value",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "industry_qidian",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "firstIndustry",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "secondIndustry",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "thirdIndustry",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "fourthIndustry",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "originCode",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "industry_tyc",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "firstIndustry",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "fourthIndustry",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "originCode",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "secondIndustry",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "thirdIndustry",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "industry_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "industry_winhc",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "firstIndustry",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "fourthIndustry",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "secondIndustry",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "thirdIndustry",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "originCode",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "investor",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "iprPledgeList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "iprName",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "iprPledgeAlterationList",
                                "nullable": True,
                                "type": {
                                    "containsNull": True,
                                    "elementType": {
                                        "fields": [
                                            {
                                                "metadata": {

                                                },
                                                "name": "alterItem",
                                                "nullable": True,
                                                "type": "string"
                                            },
                                            {
                                                "metadata": {

                                                },
                                                "name": "preAlter",
                                                "nullable": True,
                                                "type": "string"
                                            },
                                            {
                                                "metadata": {

                                                },
                                                "name": "postAlter",
                                                "nullable": True,
                                                "type": "string"
                                            },
                                            {
                                                "metadata": {

                                                },
                                                "name": "alter_date",
                                                "nullable": True,
                                                "type": "string"
                                            }
                                        ],
                                        "type": "struct"
                                    },
                                    "type": "array"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "iprPledgePeriod",
                                "nullable": True,
                                "type": {
                                    "fields": [
                                        {
                                            "metadata": {

                                            },
                                            "name": "begin",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "end",
                                            "nullable": True,
                                            "type": "string"
                                        }
                                    ],
                                    "type": "struct"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "iprPledgePublicDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "iprPledgeRevokeDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "iprPledgeRevokeReason",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "iprPledgeeName",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "iprPledgorName",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "iprRegisterNum",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "iprStatus",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "iprType",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "isAnomaly",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {

                },
                "name": "isList",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {

                },
                "name": "is_address_changed",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {

                },
                "name": "is_branch_office_list_changed",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {

                },
                "name": "is_holder_list_changed",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {

                },
                "name": "is_top",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {

                },
                "name": "judicialAssistanceList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "court",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "defaulterName",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "equityAmount",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "equityChangeInfo",
                                "nullable": True,
                                "type": {
                                    "fields": [
                                        {
                                            "metadata": {

                                            },
                                            "name": "court",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "case",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "decisionNumber",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "noticeNumber",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "defaulterName",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "defaulterEquityAmount",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "defaulterIdType",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "defaulterIdNumber",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "assigneeName",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "executionDate",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "assigneeIdType",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "assigneeIdNumber",
                                            "nullable": True,
                                            "type": "string"
                                        }
                                    ],
                                    "type": "struct"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "freezeInfo",
                                "nullable": True,
                                "type": {
                                    "fields": [
                                        {
                                            "metadata": {

                                            },
                                            "name": "court",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "case",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "decisionNumber",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "noticeNumber",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "defaulterName",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "defaulterEquityAmount",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "defaulterIdType",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "defaulterIdNumber",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "beginDate",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "endDate",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "term",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "publicationDate",
                                            "nullable": True,
                                            "type": "string"
                                        }
                                    ],
                                    "type": "struct"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "freezeKeepInfo",
                                "nullable": True,
                                "type": {
                                    "fields": [
                                        {
                                            "metadata": {

                                            },
                                            "name": "court",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "case",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "decisionNumber",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "noticeNumber",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "defaulterName",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "defaulterEquityAmount",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "defaulterIdType",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "defaulterIdNumber",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "beginDate",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "endDate",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "term",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "publicationDate",
                                            "nullable": True,
                                            "type": "string"
                                        }
                                    ],
                                    "type": "struct"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "freezeRemoveInfo",
                                "nullable": True,
                                "type": {
                                    "fields": [
                                        {
                                            "metadata": {

                                            },
                                            "name": "court",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "case",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "decisionNumber",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "noticeNumber",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "defaulterName",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "defaulterEquityAmount",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "defaulterIdType",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "defaulterIdNumber",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "freezeRemoveDate",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "publicationDate",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "invalidationReason",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "invalidationDate",
                                            "nullable": True,
                                            "type": "string"
                                        }
                                    ],
                                    "type": "struct"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "invalidationInfo",
                                "nullable": True,
                                "type": {
                                    "fields": [
                                        {
                                            "metadata": {

                                            },
                                            "name": "reason",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "date",
                                            "nullable": True,
                                            "type": "string"
                                        }
                                    ],
                                    "type": "struct"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "jaDeleted",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "noticeNumber",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "status",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "last_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "legalRepresentative",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "licenseList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "authority",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "beginDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "content",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "endDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "grade",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "id",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "issueDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "name",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "status",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "type",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "licensingDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "liquidationInfo",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "manager",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "memberList",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "location",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "lat",
                            "nullable": True,
                            "type": "double"
                        },
                        {
                            "metadata": {

                            },
                            "name": "lng",
                            "nullable": True,
                            "type": "double"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "memberList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "name",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "position",
                                "nullable": True,
                                "type": {
                                    "containsNull": True,
                                    "elementType": "string",
                                    "type": "array"
                                }
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "micro",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {

                },
                "name": "microPolicySupport",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "authority",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "basis",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "content",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "date",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "value",
                                "nullable": True,
                                "type": "double"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "mobileAppEnterpriseType",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "mortgageInfoList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "alterationList",
                                "nullable": True,
                                "type": {
                                    "containsNull": True,
                                    "elementType": {
                                        "fields": [
                                            {
                                                "metadata": {

                                                },
                                                "name": "date",
                                                "nullable": True,
                                                "type": "string"
                                            },
                                            {
                                                "metadata": {

                                                },
                                                "name": "content",
                                                "nullable": True,
                                                "type": "string"
                                            }
                                        ],
                                        "type": "struct"
                                    },
                                    "type": "array"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "amount",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "authority",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "date",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "guaranteedCreditorInfo",
                                "nullable": True,
                                "type": {
                                    "fields": [
                                        {
                                            "metadata": {

                                            },
                                            "name": "type",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "scope",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "amount",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "coinType",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "term",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "remark",
                                            "nullable": True,
                                            "type": "string"
                                        }
                                    ],
                                    "type": "struct"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "id",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "mortgageeList",
                                "nullable": True,
                                "type": {
                                    "containsNull": True,
                                    "elementType": {
                                        "fields": [
                                            {
                                                "metadata": {

                                                },
                                                "name": "name",
                                                "nullable": True,
                                                "type": "string"
                                            },
                                            {
                                                "metadata": {

                                                },
                                                "name": "identificationType",
                                                "nullable": True,
                                                "type": "string"
                                            },
                                            {
                                                "metadata": {

                                                },
                                                "name": "identifacationNo",
                                                "nullable": True,
                                                "type": "string"
                                            },
                                            {
                                                "metadata": {

                                                },
                                                "name": "address",
                                                "nullable": True,
                                                "type": "string"
                                            }
                                        ],
                                        "type": "struct"
                                    },
                                    "type": "array"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "pawnList",
                                "nullable": True,
                                "type": {
                                    "containsNull": True,
                                    "elementType": {
                                        "fields": [
                                            {
                                                "metadata": {

                                                },
                                                "name": "name",
                                                "nullable": True,
                                                "type": "string"
                                            },
                                            {
                                                "metadata": {

                                                },
                                                "name": "owner",
                                                "nullable": True,
                                                "type": "string"
                                            },
                                            {
                                                "metadata": {

                                                },
                                                "name": "detail",
                                                "nullable": True,
                                                "type": "string"
                                            },
                                            {
                                                "metadata": {

                                                },
                                                "name": "remark",
                                                "nullable": True,
                                                "type": "string"
                                            }
                                        ],
                                        "type": "struct"
                                    },
                                    "type": "array"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "publicationDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "revokeInfo",
                                "nullable": True,
                                "type": {
                                    "fields": [
                                        {
                                            "metadata": {

                                            },
                                            "name": "date",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "reason",
                                            "nullable": True,
                                            "type": "string"
                                        }
                                    ],
                                    "type": "struct"
                                }
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "name",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "nameAlterList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "alterDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "alterSource",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "postAlter",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "preAlter",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "preAlterMd5",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "nameId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "nameMd5",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "news_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "officeAddress",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "address",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "source",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "operBrandList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "operStatus",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "name",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "value",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "operator",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "orgCode",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "orgType",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "paidupCapital",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "coinType",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "value",
                            "nullable": True,
                            "type": "double"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "parentCompany",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "address",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "name",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "regNo",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "patentList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "address",
                                "nullable": True,
                                "type": {
                                    "fields": [
                                        {
                                            "metadata": {

                                            },
                                            "name": "value",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "name",
                                            "nullable": True,
                                            "type": "string"
                                        }
                                    ],
                                    "type": "struct"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "agent",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "agentorList",
                                "nullable": True,
                                "type": {
                                    "containsNull": True,
                                    "elementType": "string",
                                    "type": "array"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "aplydate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "inventorList",
                                "nullable": True,
                                "type": {
                                    "containsNull": True,
                                    "elementType": "string",
                                    "type": "array"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "mainTypeNumList",
                                "nullable": True,
                                "type": {
                                    "containsNull": True,
                                    "elementType": "string",
                                    "type": "array"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "name",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "number",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "type",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "typeNumList",
                                "nullable": True,
                                "type": {
                                    "containsNull": True,
                                    "elementType": "string",
                                    "type": "array"
                                }
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "patentNo",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {

                },
                "name": "postCode",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "principal",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "punishmentList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "authority",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "content",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "decisionDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "desc",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "id",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "is_deleted",
                                "nullable": True,
                                "type": "long"
                            },
                            {
                                "metadata": {

                                },
                                "name": "publishDate",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "punishmentFileLink",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "type",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "regCapital",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "coinType",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "origin",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "unit",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "value",
                            "nullable": True,
                            "type": "double"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "regCity",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "regNumber",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "regProvince",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "region",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "city",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "district",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "name",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "province",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {

                            },
                            "name": "value",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {

                },
                "name": "registrationAuthority",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "repeatNameId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "revokeDate",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "revokeReason",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "shortNameList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "simpleCancellationList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "code",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "is_deleted",
                                "nullable": True,
                                "type": "long"
                            },
                            {
                                "metadata": {

                                },
                                "name": "name",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "publicRange",
                                "nullable": True,
                                "type": {
                                    "fields": [
                                        {
                                            "metadata": {

                                            },
                                            "name": "begin",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "end",
                                            "nullable": True,
                                            "type": "string"
                                        }
                                    ],
                                    "type": "struct"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "registrationAuthority",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "simpleCancellationResult",
                                "nullable": True,
                                "type": {
                                    "fields": [
                                        {
                                            "metadata": {

                                            },
                                            "name": "result",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "date",
                                            "nullable": True,
                                            "type": "string"
                                        }
                                    ],
                                    "type": "struct"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "objectionInformation",
                                "nullable": True,
                                "type": {
                                    "fields": [
                                        {
                                            "metadata": {

                                            },
                                            "name": "objectionApplicant",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "objectionContent",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {

                                            },
                                            "name": "objectionTime",
                                            "nullable": True,
                                            "type": "string"
                                        }
                                    ],
                                    "type": "struct"
                                }
                            },
                            {
                                "metadata": {

                                },
                                "name": "title",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "socialCreditCode",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "source",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "szEntUrl",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "turnoverList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "coinType",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "max",
                                "nullable": True,
                                "type": "double"
                            },
                            {
                                "metadata": {

                                },
                                "name": "min",
                                "nullable": True,
                                "type": "double"
                            },
                            {
                                "metadata": {

                                },
                                "name": "period",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "tyc_annual_list",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "last_change_holder_list_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "last_change_branch_office_list_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "last_alteration_list_change_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "last_licensing_date_change_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "last_anomaly_list_change_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "last_punishment_list_change_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "last_member_list_change_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {

                },
                "name": "weblink",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "alter_failed",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {

                },
                "name": "holder_failed",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {

                },
                "name": "member_failed",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {

                },
                "name": "anomaly_failed",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {

                },
                "name": "groupName",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "groupAbbreviation",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "groupMemberList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {

                                },
                                "name": "memName",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "pcomaGrop",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "uniscId",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {

                                },
                                "name": "grpmemType",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "no_accurate_location",
                "nullable": True,
                "type": "boolean"
            }
        ],
        "type": "struct"
    }
