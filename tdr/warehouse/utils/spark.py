# -*- coding: utf-8 -*-
"""
    __init__.py
    ~~~~~~~

    spark常见逻辑封装

    :author: <PERSON>
    :copyright: (c) 2022, Tungee
    :date created: 2022-12-19
    :python version:
"""

import json
from pyspark.rdd import RDD
from tdr.common.utils.json_util import (
    object_hook
)


def get_rdd_from_json_file(_spark_context, _json_file_path) -> RDD:
    """
    从json文件读取RDD并转为dict
    :param _spark_context: SparkContext
    :param _json_file_path: json 文件路径
    :return: RDD
    """

    def load_json_line(line):
        line = line.strip()
        try:
            return json.loads(line, object_hook=object_hook)
        except:
            return None

    return _spark_context.textFile(
        _json_file_path
    ).map(
        load_json_line
    ).filter(
        lambda doc: doc
    )


def get_rdd_from_bson_file(_spark_context, _bson_file_path) -> RDD:
    """
    从bson文件读取RDD并转为dict
    :param _spark_context: SparkContext
    :param _bson_file_path: bson 文件路径
    :return: RDD
    """
    # CAUTION：读取BSON文件，务必加上这两句
    import pymongo_spark
    pymongo_spark.activate()
    return _spark_context.BSONFileRDD(_bson_file_path)


def get_rdd_from_file(_spark_context, _file_path) -> RDD:
    """
    读取文件生成RDD，当文件是BSON类型是，_file_path 需要以 bson 结尾
    :param _spark_context: SparkContext
    :param _file_path: 文件路径
    :return:
    """
    if _file_path.endswith('bson'):
        get_rdd_func = get_rdd_from_bson_file
    else:
        get_rdd_func = get_rdd_from_json_file
    rdd = get_rdd_func(_spark_context, _file_path)
    return rdd
