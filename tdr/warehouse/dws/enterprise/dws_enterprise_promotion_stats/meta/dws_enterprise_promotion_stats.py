# -*- coding: utf-8 -*-
"""
    dws_enterprise_promotion_stats
    ~~~~~~~
    
    Description
    
    :author: <PERSON>
    :copyright: (c) 2022, Tungee
    :date created: 2022-12-09
    :python version: 
"""
from tdr.common.constant.common import FormatType
from tdr.warehouse.dwm.enterprise.dwm_enterprise_promotion_competitor.meta.dwm_enterprise_promotion_competitor import \
    DwmEnterprisePromotionCompetitor
from tdr.warehouse.ods.ods_promotion.meta.ods_promotion import OdsPromotion


class DwsEnterprisePromotionStats(object):
    NAME = 'dws_enterprise_promotion_stats'

    FORMAT_TYPE = FormatType.json

    class Field:
        id = '_id'
        hasPrmt = 'hasPrmt'  # 是否有推广
        lastPrmtTime = 'lastPrmtTime'  # 最新推广时间
        prmtCompetitorsNumber = 'prmtCompetitorsNumber'  # 推广竞对数量
        prmtKeyLastMonth = 'prmtKeyLastMonth'  # 最近新一个月的推广关键词
        prmtKeys = 'prmtKeys'  # 推广关键词
        prmtKeysNumber = 'prmtKeysNumber'  # 推广词数量
        prmtLinks = 'prmtLinks'  # 推广链接
        prmtLinksNumber = 'prmtLinksNumber'  # 推广链接数量
        prmtSources = 'prmtSources'  # 推广源
        prmtText = 'prmtText'  # 推广正文
        tabStats = 'tabStats'  # tab 页面统计

    class TabStatsField:
        operating_info_promotion = 'operating_info::promotion'

    INPUTS = [OdsPromotion, DwmEnterprisePromotionCompetitor]

    # Spark Schema
    SPARK_SCHEMA = {
        "fields": [
            {
                "metadata": {

                },
                "name": "_id",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "hasPrmt",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {

                },
                "name": "lastPrmtTime",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {

                },
                "name": "prmtCompetitorsNumber",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {

                },
                "name": "prmtKeyLastMonth",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "prmtKeys",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "prmtKeysNumber",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {

                },
                "name": "prmtLinks",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "prmtLinksNumber",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {

                },
                "name": "prmtSources",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "prmtText",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {

                },
                "name": "tabStats",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {

                            },
                            "name": "operating_info::promotion",
                            "nullable": True,
                            "type": "long"
                        }
                    ],
                    "type": "struct"
                }
            }
        ],
        "type": "struct"
    }
