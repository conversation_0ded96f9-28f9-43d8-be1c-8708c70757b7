# -*- coding: utf-8 -*-
"""
    dws_enterprise_promotion_stas
    ~~~~~~~
    
    企业推广信息统计
    
    :author: <PERSON>
    :copyright: (c) 2022, Tungee
    :date created: 2022-12-12
    :python version: 
"""
import sys
from collections import defaultdict
from datetime import datetime, timedelta

from pyspark.sql import SparkSession
from pyspark.sql import functions as F

from tdr.common.constant.module import ModuleNameConstant
from tdr.common.utils.spark.spark_utils import persist_rdd
from tdr.common.utils.time_helper import convert_arbitrary_date_format
from tdr.warehouse.dwm.enterprise.dwm_enterprise_promotion_competitor.meta.dwm_enterprise_promotion_competitor import \
    DwmEnterprisePromotionCompetitor
from tdr.warehouse.dws.enterprise.dws_enterprise_promotion_stats.meta.dws_enterprise_promotion_stats import \
    DwsEnterprisePromotionStats
from tdr.warehouse.ods.ods_promotion.meta.ods_promotion import OdsPromotion
from tdr.warehouse.utils.spark import get_rdd_from_file
JOB_NAME = 'DW_{}_{}'.format(ModuleNameConstant.enterprise, DwsEnterprisePromotionStats.NAME)


def promotion_filter(promotion_dict):
    """
    过滤数据：缺少企业映射、缺少推广源、缺少推广关键词
    """
    for k in [OdsPromotion.Field.nameId, OdsPromotion.Field.prmtLink]:
        if k not in promotion_dict:
            return False
    return True


def promotion_flat_map(promotion_dict):
    """
    推广维度:
    1. key: nameId
    2. key: nameId + prmtLink
    3. key: promKeys
    """
    name_id = promotion_dict['nameId']
    source = promotion_dict['prmtSource']
    link = promotion_dict['prmtLink']
    key = promotion_dict['prmtKey']
    time = promotion_dict.get('prmtTime')

    # 根据企业进行计算的维度
    name_id_data = {
        DwsEnterprisePromotionStats.Field.prmtSources: [source],
        DwsEnterprisePromotionStats.Field.prmtLinks: [link],
        DwsEnterprisePromotionStats.Field.prmtKeys: [key],
        DwsEnterprisePromotionStats.Field.lastPrmtTime: time,
        DwsEnterprisePromotionStats.Field.tabStats: {
            DwsEnterprisePromotionStats.TabStatsField.operating_info_promotion: 1
        },
    }
    if OdsPromotion.Field.prmtTitle in promotion_dict:
        name_id_data[DwsEnterprisePromotionStats.Field.prmtText] = [promotion_dict[OdsPromotion.Field.prmtTitle]]

    prmt_time = convert_arbitrary_date_format(time)
    now = datetime.strptime(datetime.now().strftime('%Y-%m-%d'), '%Y-%m-%d')
    if prmt_time and prmt_time >= (now - timedelta(days=30)):
        name_id_data[DwsEnterprisePromotionStats.Field.prmtKeyLastMonth] = [key]
    else:
        name_id_data[DwsEnterprisePromotionStats.Field.prmtKeyLastMonth] = []

    return name_id, name_id_data


def promotion_reduce(x_dict, y_dict):
    for k in [
        DwsEnterprisePromotionStats.Field.prmtSources,
        DwsEnterprisePromotionStats.Field.prmtLinks,
        DwsEnterprisePromotionStats.Field.prmtKeys,
        DwsEnterprisePromotionStats.Field.prmtKeyLastMonth,
    ]:
        x_dict[k] = list(set(x_dict[k] + y_dict[k]))

        # 合并推广文案
    texts = list(
        set(
            x_dict.get(DwsEnterprisePromotionStats.Field.prmtText, list())
            + y_dict.get(DwsEnterprisePromotionStats.Field.prmtText, list())
        )
    )
    if len(texts) > 0:
        x_dict[DwsEnterprisePromotionStats.Field.prmtText] = texts

    # 推广时间取更新的
    if x_dict[DwsEnterprisePromotionStats.Field.lastPrmtTime] < y_dict[DwsEnterprisePromotionStats.Field.lastPrmtTime]:
        x_dict[DwsEnterprisePromotionStats.Field.lastPrmtTime] = y_dict[DwsEnterprisePromotionStats.Field.lastPrmtTime]

    tab_stats = defaultdict(int)
    for key, value in x_dict.get(DwsEnterprisePromotionStats.Field.tabStats, {}).items():
        tab_stats[key] += value
    for key, value in y_dict.get(DwsEnterprisePromotionStats.Field.tabStats, {}).items():
        tab_stats[key] += value
    if tab_stats:
        x_dict[DwsEnterprisePromotionStats.Field.tabStats] = tab_stats
    return x_dict


def map_format_promotion(compute_data):
    key, (doc, competitor_size) = compute_data
    doc[DwsEnterprisePromotionStats.Field.id] = key

    doc[DwsEnterprisePromotionStats.Field.hasPrmt] = 1
    doc[DwsEnterprisePromotionStats.Field.prmtLinksNumber] = len(doc[DwsEnterprisePromotionStats.Field.prmtLinks])
    doc[DwsEnterprisePromotionStats.Field.prmtKeysNumber] = len(doc[DwsEnterprisePromotionStats.Field.prmtKeys])
    doc[DwsEnterprisePromotionStats.Field.prmtCompetitorsNumber] = competitor_size or 0

    return doc


def run(*args):
    spark = SparkSession.builder.appName(JOB_NAME).getOrCreate()
    sc = spark.sparkContext
    ods_promotion_input, dwm_enterprise_promotion_competitor_input, dwm_enterprise_promotion_stats_output, n_partition = args
    n_partition = int(n_partition)
    # ============== 读取数据 =============== #
    dwm_ent_prmt_competitor_df = spark.read.json(dwm_enterprise_promotion_competitor_input,
                                                 schema=DwmEnterprisePromotionCompetitor.SPARK_SCHEMA)
    ods_promotion_rdd = get_rdd_from_file(sc, ods_promotion_input)
    # ============== 计算逻辑 =============== #
    prmt_competitor_size_rdd = dwm_ent_prmt_competitor_df.select(
        DwmEnterprisePromotionCompetitor.Field.nameId
    ).groupby(
        DwmEnterprisePromotionCompetitor.Field.nameId
    ).agg(
        F.count(DwmEnterprisePromotionCompetitor.Field.nameId).alias(
            DwsEnterprisePromotionStats.Field.prmtCompetitorsNumber)
    ).rdd.map(
        lambda row: (row[DwmEnterprisePromotionCompetitor.Field.nameId],
                     row[DwsEnterprisePromotionStats.Field.prmtCompetitorsNumber])
    )

    dwm_enterprise_promotion_stats_rdd = ods_promotion_rdd.filter(
        promotion_filter
    ).map(
        promotion_flat_map
    ).reduceByKey(
        promotion_reduce
    ).leftOuterJoin(
        prmt_competitor_size_rdd
    ).map(
        map_format_promotion
    )

    persist_rdd(dwm_enterprise_promotion_stats_rdd, dwm_enterprise_promotion_stats_output, numPartitions=n_partition)


if __name__ == '__main__':
    run(*sys.argv[1:])