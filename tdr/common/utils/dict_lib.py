#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
    ~~~~~~~~~~~~~~~~~~~~~~~

    Dict 工具类

    :author: Katrinafu
    :copyright: (c) 2022, Tungee
    :date created: 2022-05-29
    :python version: 2.7

"""

from __future__ import unicode_literals
import sys
from datetime import datetime
from tdr.common.utils.time_helper import convert_arbitrary_date_format

# try:
#     reload(sys)
#     sys.setdefaultencoding('utf-8')
# except:
#     pass


IS_PYTHON_2 = True
if sys.version_info.major != 2:
    IS_PYTHON_2 = False


def get_dict_item_val(dic, key, valid_types, default_val=None, pop=False):
    """
    获取字段中的某一个字段的值
    :param dic: dict 文档
    :param key: 维度名称
    :param valid_types: 预期的数据类型
    :param default_val: 默认值
    :param pop: 是否从 dic 中 pop 掉目标字段
    :return:
    """
    val = dic.get(key)
    if pop and val is not None:
        dic.pop(key)
    if not isinstance(val, valid_types):
        return default_val
    else:
        return val


STRING_COMPATIBLE_TYPES = (str,)
DIGITAL_COMPATIBLE_TYPES = (int, float)
DATETIME_COMPATIBLE_TYPES = (datetime, str)


def get_dict_item_val_str(dic, key, default_val='', pop=False):
    """
    获取字段中的某一个str字段的值
    :param dic: dict 文档
    :param key: 维度名称
    :param default_val: 默认值
    :param pop: 是否从 dic 中 pop 掉目标字段
    :return:
    """
    return get_dict_item_val(dic, key, STRING_COMPATIBLE_TYPES, default_val, pop)


def get_dict_item_val_list(dic, key, default_val=None, pop=False):
    """
    获取字段中的某一个list字段的值
    :param dic: dict 文档
    :param key: 维度名称
    :param default_val: 默认值
    :param pop: 是否从 dic 中 pop 掉目标字段
    :return:
    """
    if default_val is None:
        default_val = []
    return get_dict_item_val(dic, key, list, default_val, pop)


def get_dict_item_val_dict(dic, key, default_val=None, pop=False):
    """
    获取字段中的某一个dict字段的值
    :param dic: dict 文档
    :param key: 维度名称
    :param default_val: 默认值
    :param pop: 是否从 dic 中 pop 掉目标字段
    :return:
    """
    if default_val is None:
        default_val = {}
    return get_dict_item_val(dic, key, dict, default_val, pop)


def get_dict_item_val_int(dic, key, default_val=None, pop=False):
    """
    获取字段中的某一个int字段的值
    :param dic: dict 文档
    :param key: 维度名称
    :param default_val: 默认值
    :param pop: 是否从 dic 中 pop 掉目标字段
    :return:
    """
    if default_val is None:
        default_val = 0
    rst = get_dict_item_val(
        dic, key, DIGITAL_COMPATIBLE_TYPES, default_val, pop)
    if isinstance(rst, float):
        rst = int(rst)
    return rst


def get_dict_item_val_digital(dic, key, default_val=None, pop=False):
    """
    获取字段中的某一个数字字段的值
    :param dic: dict 文档
    :param key: 维度名称
    :param default_val: 默认值
    :param pop: 是否从 dic 中 pop 掉目标字段
    :return:
    """
    if default_val is None:
        default_val = 0
    return get_dict_item_val(dic, key, DIGITAL_COMPATIBLE_TYPES, default_val, pop)


def get_dict_item_val_datetime(dic, key, default_val=None, pop=False):
    """
    获取字段中的某一个datetime字段的值
    :param dic: dict 文档
    :param key: 维度名称
    :param default_val: 默认值
    :param pop: 是否从 dic 中 pop 掉目标字段
    :return:
    """
    return get_dict_item_val(dic, key, datetime, default_val, pop)


def get_dict_item_val_datetime_smart(dic, key, default_val=None, pop=False):
    """
    目前支持的提取类型：字符串、datetime
    :param dic: dict 文档
    :param key: 维度名称
    :param default_val: 默认值
    :param pop: 是否从 dic 中 pop 掉目标字段
    :return:
    """
    val = get_dict_item_val(
        dic, key, DATETIME_COMPATIBLE_TYPES, default_val='', pop=pop)
    if isinstance(val, STRING_COMPATIBLE_TYPES):
        val = convert_arbitrary_date_format(val)
    return val or default_val


def cut_dict_fields(dic, fields, inplace=False, nullable=False):
    """
    裁剪目标维度
    :param dic: 文档
    :param fields: 预期维度
    :param inplace: 是否在原 dic 对象上裁剪
    :param nullable: 是否保留值为 None 的维度
    :return:
    """
    if inplace:
        for key in set(dic.iterkeys()).difference(fields):
            dic.pop(key)
        return dic
    else:
        fields_set = set(fields)
        if nullable:
            return {key: val for key, val in dic.items() if key in fields_set}
        else:
            return {key: val for key, val in dic.items() if key in fields_set and val is not None}


def dict_projection(dic, projection):
    """
    dict 裁剪转换为一个新的 dict 对象，维度名会根据projection转换
    :param dic: dict 对象
    :param projection: list of dict(新维度名，旧维度名)
    :return:
    """
    rst = {}
    for key_rst, key_origin in projection.items():
        val = dic.get(key_origin)
        if val is not None:
            rst[key_rst] = val
    return rst


def dict_update_and_return(dict_base, *oth_dicts):
    """
    更新一个 dict 并返回原 dict 对象
    :param dict_base: 被更新 dict
    :param oth_dicts: 更新维度 dict
    :return:
    """
    for dic in oth_dicts:
        dict_base.update(dic)
    return dict_base


def pop_dict_fields(dic, fields, inplace=False):
    """
    从一个 dict pop 掉维度
    :param dic: dict 对象
    :param fields: 需要 pop 掉的维度
    :param inplace: 是否在原 dict 对象上操作，False: 返回结果会是一个新的 dict 对象；True: 返回结果仍是传入的 dic
    :return:
    """
    if not dic:
        return {}
    if inplace:
        dic_ref = dic
    else:
        dic_ref = dic.copy()
    for field in fields:
        if field in dic_ref:
            dic_ref.pop(field)
    return dic_ref


def exist_dict_fields(dic, fields):
    """
    是否存在预期维度
    :param dic: dict 文档
    :param fields: 维度列表
    :return: bool, True: 存在；False: 不存在
    """
    if not dic or not fields:
        return False

    for field in fields:
        if field not in dic:
            return False
    return True


# if __name__ == '__main__':
#     for src_date in [
#         '2020-01-02 04:30:31',
#         '2020-01-02',
#         '2020-01',
#         datetime(2020, 9, 3, 9, 16, 35)
#     ]:
#         doc = { 'time': src_date }
#         got_date = get_dict_item_val_datetime_smart(doc['time'])
#         print('get_dict_item_val_datetime_smart(%s) = %s' % ( str(src_date), str(got_date) ))
