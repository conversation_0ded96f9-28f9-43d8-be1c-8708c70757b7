#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    ~~~~~~~~~~~~~~~~~~~~~~~
   
    json 工具类
   
    :author: 张平
    :copyright: (c) 2021, Tungee
    :date created: 2021-06-28
    :python version: 3.5
   
"""

import decimal
import json
from datetime import datetime

from bson import BSON

from tdr.common.utils.time_helper import datetime_encode, string2datetime


class MyJSONEncoder(json.JSONEncoder):
    """自定义 json 解码器"""

    def default(self, obj):
        if isinstance(obj, (datetime,)):
            return {"val": datetime_encode(obj), "_spec_type": "datetime"}
        elif isinstance(obj, (decimal.Decimal,)):
            return {"val": str(obj), "_spec_type": "decimal"}
        else:
            return json.JSONEncoder.default(self, obj)


class SimpleJSONEncoder(json.JSONEncoder):
    """
    简化json序列化
        datetime、decimal转字符串
    """
    def default(self, obj):
        if isinstance(obj, (datetime,)):
            return datetime_encode(obj)
        elif isinstance(obj, (decimal.Decimal,)):
            return str(obj)
        else:
            return json.JSONEncoder.default(self, obj)

CONVERTERS = {
    'datetime': string2datetime,
    'decimal': decimal.Decimal,
}


def object_hook(obj):
    """特殊数据类型处理方法"""
    _spec_type = obj.get('_spec_type')
    if not _spec_type:
        return obj

    if _spec_type in CONVERTERS:
        return CONVERTERS[_spec_type](obj['val'])
    else:
        raise Exception('Unknown {}'.format(_spec_type))


def json_to_bson(input_json_path, output_bson_path):
    """
    json 转换为 bson
    :param input_json_path: json 文件输入路径
    :param output_bson_path: bson 文件输出路径
    :return:
    """
    bson_encode = BSON.encode
    json_loads = json.loads
    with open(input_json_path, 'r') as in_file, \
            open(output_bson_path, 'wb') as out_file:
        for line in in_file:
            line = line.strip()
            doc = json_loads(line.strip())
            out_file.write(bson_encode(doc))


def loads_data(data: str) -> dict:
    """
    loads JSON string
    @param data: json字符串
    @return: dict
    """
    try:
        doc = json.loads(data.strip(), object_hook=object_hook)
        return doc
    except Exception as e:
        raise Exception('Error: parse data failed!', e)


def dumps_json(doc):
    """
    字典转JSON字符串
    @param doc: dict 文档
    @return: json字符串
    """
    assert isinstance(doc, dict), "doc must be `dict` type!"
    return json.dumps(doc, ensure_ascii=False, cls=MyJSONEncoder)
