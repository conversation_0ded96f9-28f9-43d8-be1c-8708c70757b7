#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    md5_util
    ~~~~~~~~~~~~~~~~~~~~~~~

    MD5 工具类

    :author: <PERSON><PERSON><PERSON><PERSON> :D
    :copyright: (c) 2016, Tungee
    :date created: 2016-12-07
    :python version: 2.7

"""

from binascii import a2b_hex, b2a_base64
from hashlib import md5


class MD5Exception(Exception):
    """MD5异常"""
    pass


def str_32_md5(target):
    """
    字符串转32位MD5
    :param target: 字符串
    :return: 32位MD5字符串
    """
    if target is None:
        raise MD5Exception()
    if isinstance(target, str):
        #     return md5(target).hexdigest()
        # if isinstance(target, unicode):
        return md5(target.encode('utf-8')).hexdigest()
    if isinstance(target, int) or isinstance(target, float):
        return md5(str(target)).hexdigest()
    raise MD5Exception()


def str_md5(target, digit=32):
    """
    字符串转MD5
    :param target: 字符串
    :param digit: MD5字符串位数
    :return:
    """
    if digit == 32:
        return str_32_md5(target)
    elif digit == 16:
        return str_32_md5(target)[8:-8]
    raise MD5Exception()


def str_md5_base64(target, digit=32):
    """
    字符串转MD5再base64
    :param target: 字符串
    :param digit: MD5字符串长度
    :return:
    """
    value = str_md5(target, digit)
    return b2a_base64(a2b_hex(value)).decode("utf-8").rstrip('\n')
