#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    ~~~~~~~~~~~~~~~~~~~~~~~
   
    通用或者不知道分类的常量
   
    :author: 张平
    :copyright: (c) 2021, Tungee
    :date created: 2021-05-22
    :python version: 3.5
   
"""
# 下载oss文件线程数
download_max_workers = 16

ENTREPRISE_PROFIX = "enterprise"

NPO_PROFIX = "npo"

LAW_PROFIX = "law"

mongo_doc_size = 16 * 1024 * 1024  # mongo文档大小最大16M

DEBUG = False


class CommonConstant(object):
    """用于es import 自定义脚本名称"""
    es_preprocess = "es_preprocess"

    # 用于时间格式化的常量
    TIME_FORMAT = '%Y-%m-%d %H:%M:%S'

    # 用于时间格式化的常量
    TIME_FORMAT_ONLY_DATE = '%Y-%m-%d'


class ImportFileType(object):
    """
    导入文件类型

    * bson: bson 文件
    * json: json 文件
    * csv: csv 文件
    """
    BSON = "bson"
    JSON = "json"
    CSV = "csv"


COIN_MAPPING = {
    "美元": 6.67,
    "港元": 0.85,
    "加拿大元": 5.08,
    "英镑": 8.85,
    "澳大利亚元": 4.96,
    "丹麦克朗": 1.05,
    "欧元": 7.81,
    "韩元": 0.006,
    "新加坡元": 4.90,
    "日元": 0.059,
    "德国马克": 3.82,
    "瑞士法郎": 6.68,
    "印度卢比": 0.097,
    "卢布": 0.107,
}


def is_valid(doc, field, _tp):
    """
    判断doc里field字段是否为_tp类型
    :param doc: dict 对象
    :param field: 字段名
    :param _tp: 数据类型元组
    :return:
    """
    if not isinstance(doc, dict):
        return False
    if '.' in field:
        parent_field, child_field = field.split('.', 1)
        if not doc.get(parent_field) or not isinstance(doc[parent_field], dict):
            return False
        return is_valid(doc[parent_field], child_field, _tp)
    else:
        return True if doc.get(field) and isinstance(doc[field], _tp) else False


def clean_string(value):
    """
    清洗字符串
    1.（ 转 (
    2. ）转 )
    3. ，转 ,

    :param value: 字符串
    :return: 清洗过的字符串
    """
    return value.strip().replace('（', '(').replace('）', ')').replace('，', ',')


OPER_STATUS_MAPPING = {
    "吊销": {
        "first": "吊销",
        "second": "吊销"
    },
    "注吊销": {
        "first": "吊销",
        "second": "吊销"
    },
    "被吊销": {
        "first": "吊销",
        "second": "吊销"
    },
    "个体暂时吊销": {
        "first": "吊销",
        "second": "吊销"
    },
    "吊销后未注销": {
        "first": "吊销",
        "second": "吊销未注销"
    },
    "吊销,未注销": {
        "first": "吊销",
        "second": "吊销未注销"
    },
    "吊销未注销": {
        "first": "吊销",
        "second": "吊销未注销"
    },
    "吊销并注销": {
        "first": "注销",
        "second": "吊销已注销"
    },
    "吊销,已注销": {
        "first": "注销",
        "second": "吊销已注销"
    },
    "吊销,已注销(个体)": {
        "first": "注销",
        "second": "吊销已注销"
    },
    "吊销已注销": {
        "first": "注销",
        "second": "吊销已注销"
    },
    "吊销,已注销注销": {
        "first": "注销",
        "second": "吊销已注销"
    },
    "吊销后注销": {
        "first": "注销",
        "second": "吊销已注销"
    },
    "吊销,已注销,注销": {
        "first": "注销",
        "second": "吊销已注销"
    },
    "吊销企业": {
        "first": "吊销",
        "second": "已吊销"
    },
    "已吊销": {
        "first": "吊销",
        "second": "已吊销"
    },
    "已经吊销": {
        "first": "吊销",
        "second": "已吊销"
    },
    "撤销": {
        "first": "其他",
        "second": "撤销"
    },
    "撤销登记": {
        "first": "其他",
        "second": "撤销"
    },
    "撤消登记": {
        "first": "其他",
        "second": "撤销"
    },
    "其他": {
        "first": "其他",
        "second": "其他"
    },
    "迁出": {
        "first": "其他",
        "second": "迁出"
    },
    "迁移异地": {
        "first": "其他",
        "second": "迁出"
    },
    "迁往外省市": {
        "first": "其他",
        "second": "迁出"
    },
    "迁往市外": {
        "first": "其他",
        "second": "迁出"
    },
    "迁出迁入地工商局": {
        "first": "其他",
        "second": "迁出"
    },
    "存续": {
        "first": "营业",
        "second": "存续"
    },
    "存续(经营正常)": {
        "first": "营业",
        "second": "存续"
    },
    "存续(在营、开业、在册)": {
        "first": "营业",
        "second": "存续(在营、开业、在册)"
    },
    "登记成立": {
        "first": "营业",
        "second": "登记成立"
    },
    "开业": {
        "first": "营业",
        "second": "开业"
    },
    "开业状态": {
        "first": "营业",
        "second": "开业"
    },
    "已开业": {
        "first": "营业",
        "second": "开业"
    },
    "开业/正常经营": {
        "first": "营业",
        "second": "开业"
    },
    "开业(存续)": {
        "first": "营业",
        "second": "开业"
    },
    "在业": {
        "first": "营业",
        "second": "在业"
    },
    "正常在业": {
        "first": "营业",
        "second": "在业"
    },
    "在营企业": {
        "first": "营业",
        "second": "在业"
    },
    "在营": {
        "first": "营业",
        "second": "在业"
    },
    "在营(开业)企业": {
        "first": "营业",
        "second": "在营(开业)"
    },
    "在营(开业)": {
        "first": "营业",
        "second": "在营(开业)"
    },
    "正常": {
        "first": "营业",
        "second": "正常"
    },
    "注销": {
        "first": "注销",
        "second": "注销"
    },
    "注销企业": {
        "first": "注销",
        "second": "注销"
    },
    "企业直接申请注销": {
        "first": "注销",
        "second": "注销"
    },
    "注销(简易)": {
        "first": "注销",
        "second": "注销"
    },
    "已注销": {
        "first": "注销",
        "second": "注销"
    },
    "企业已注销": {
        "first": "注销",
        "second": "注销"
    },
    "拟注销": {
        "first": "注销",
        "second": "注销"
    }
}


class FormatType:
    """ 文件存储格式 """
    bson = 'bson'
    json = 'json'
    parquet = 'parquet'
    avro = 'avro'
    text = 'text'
