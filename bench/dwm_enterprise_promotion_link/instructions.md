请完成以下任务，将现有的PySpark RDD代码转换为Spark SQL版本并进行验证：

**任务1：代码转换**
1. 基于 `bench/dwm_enterprise_promotion_link/compute.py` 创建新文件 `bench/dwm_enterprise_promotion_link/compute_sql.py`
2. 保持相同的命令行参数接口：`<input_json_path> <output_path> <num_partitions>`
3. 数据读写要求：
   - 输入：使用Spark SQL读取JSON Lines格式文件，创建临时视图
   - 输出：保存为JSON Lines格式文件
   - 移除所有RDD操作，改用DataFrame和SQL
4. 计算逻辑转换：
   - 将 `promotion_filter()` 函数逻辑转换为SQL WHERE子句
   - 将 `map_promotion_link()` 函数逻辑转换为SQL SELECT和聚合
   - 将 `reduce_promotion_link()` 函数逻辑转换为SQL GROUP BY和聚合函数
   - 将 `map_format_promotion()` 函数逻辑转换为SQL SELECT，包括MD5计算、排序、去重、取前3个等
   - 严格禁止使用任何UDF（用户自定义函数），只能使用Spark SQL内置函数
5. 保持完全相同的业务逻辑和输出数据结构

**任务2：数据验证**
1. 生成测试数据：
   - 创建符合OdsPromotion数据模型的JSON Lines测试文件
   - 包含1000行测试数据，覆盖各种边界情况（空值、重复数据、不同时间等）
   - 确保数据包含必要字段：nameId, prmtKey, prmtTime, prmtLink
2. 结果对比验证：
   - 使用相同测试数据分别运行两个版本
   - 对比输出结果的完全一致性：
     * 总行数必须相同
     * 每行数据的所有字段值必须完全匹配
     * 对比前进行数据归一化：按主键排序，处理浮点数精度等
   - 生成详细的对比报告，包括任何差异的具体描述

**任务3：文件组织**
1. 在项目根目录创建 `staging/` 子目录
2. 将以下文件放入staging目录：
   - `generate_test_data.py` - 测试数据生成脚本
   - `test_data.jsonl` - 生成的1000行测试数据
   - `run_comparison.py` - 运行两个版本并对比结果的脚本
   - `comparison_report.txt` - 详细的对比结果报告
   - `rdd_output/` - RDD版本的输出结果
   - `sql_output/` - SQL版本的输出结果

**验收标准：**
- SQL版本必须产生与RDD版本完全相同的结果
- 所有SQL查询必须是纯SQL，不包含任何Python UDF
- 测试数据要有足够的多样性来验证各种业务场景
- 对比过程要严格，确保数据完全一致