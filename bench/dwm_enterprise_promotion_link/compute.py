# -*- coding: utf-8 -*-
"""
    Self-contained and optimized version of dwm_enterprise_promotion_link_compute.py
    Removed unused code and kept only the essential dependencies.

    Original Description:

    :author: <PERSON>
    :copyright: (c) 2022, Tungee
    :date created: 2022-12-08
    :python version:
"""

# Standard library and third-party imports
import sys
import json
import decimal
from datetime import datetime
from hashlib import md5

from pyspark import RDD
from pyspark.sql import SparkSession


# ============================================================================
# MINIMAL INLINED DEPENDENCIES (only used parts)
# ============================================================================


class ModuleNameConstant(object):
    """项目模块常量 - 只保留使用的部分"""
    enterprise = 'enterprise'  # 企业信息


# --- MD5 utility functions (simplified) ---
class MD5Exception(Exception):
    """MD5异常"""
    pass


def str_md5(target, digit=32):
    """
    字符串转MD5 - 简化版本，只支持实际使用的功能
    :param target: 字符串
    :param digit: MD5字符串位数 (16 or 32)
    :return: MD5字符串
    """
    if target is None:
        raise MD5Exception()

    if isinstance(target, str):
        md5_hash = md5(target.encode('utf-8')).hexdigest()
    elif isinstance(target, (int, float)):
        md5_hash = md5(str(target).encode('utf-8')).hexdigest()
    else:
        raise MD5Exception()

    if digit == 32:
        return md5_hash
    elif digit == 16:
        return md5_hash[8:-8]
    else:
        raise MD5Exception()


# --- Time helper functions ---
def datetime_encode(dt):
    """
    datetime 转 字符串
    :param dt: 时间
    :return:
    """
    return ('{0.year:04d}-{0.month:02d}-{0.day:02d} {0.hour:02d}:{0.minute:02d}:{0.second:02d}'
            .format(dt))


def string2datetime(date_str, date_format='%Y-%m-%d %H:%M:%S'):
    """
    字符串 转 datetime
    :param date_str: 时间字符串
    :param date_format: 时间格式
    :return:
    """
    return datetime.strptime(date_str, date_format)


# --- JSON utility functions ---
class MyJSONEncoder(json.JSONEncoder):
    """自定义 json 解码器"""

    def default(self, obj):
        if isinstance(obj, datetime):
            return {"val": datetime_encode(obj), "_spec_type": "datetime"}
        elif isinstance(obj, decimal.Decimal):
            return {"val": str(obj), "_spec_type": "decimal"}
        else:
            return json.JSONEncoder.default(self, obj)


CONVERTERS = {
    'datetime': string2datetime,
    'decimal': decimal.Decimal,
}


def object_hook(obj):
    """特殊数据类型处理方法"""
    _spec_type = obj.get('_spec_type')
    if not _spec_type:
        return obj

    if _spec_type in CONVERTERS:
        return CONVERTERS[_spec_type](obj['val'])
    else:
        raise Exception('Unknown {}'.format(_spec_type))


def loads_data(data: str) -> dict:
    """
    loads JSON string
    @param data: json字符串
    @return: dict
    """
    try:
        doc = json.loads(data.strip(), object_hook=object_hook)
        return doc
    except Exception as e:
        raise Exception('Error: parse data failed!', e)


def dumps_json(doc):
    """
    字典转JSON字符串
    @param doc: dict 文档
    @return: json字符串
    """
    assert isinstance(doc, dict), "doc must be `dict` type!"
    return json.dumps(doc, ensure_ascii=False, cls=MyJSONEncoder)


# --- Spark utility functions ---
def get_rdd_from_file(_spark_context, _file_path) -> RDD:
    """
    从json文件读取RDD并转为dict
    :param _spark_context: SparkContext
    :param _file_path: json 文件路径
    :return: RDD
    """

    def load_json_line(line):
        line = line.strip()
        try:
            return json.loads(line, object_hook=object_hook)
        except:
            return None

    return _spark_context.textFile(_file_path).map(load_json_line).filter(lambda doc: doc)


def persist_rdd(rdd: RDD, path: str, numPartitions=None, shuffle=True):
    """
    持久化输出RDD
    @param rdd: rdd
    @param path: 输出路径
    @param numPartitions: 重分区数
    @param shuffle: 是否shuffle
    @param compressionCodecClass: 压缩编解码器类的完全限定类名，默认为None
    @return:
    """
    rdd = rdd.map(dumps_json)
    if numPartitions:
        rdd = rdd.coalesce(numPartitions, shuffle)
    rdd.saveAsTextFile(path)


# --- Data model classes (complete original versions) ---
class OdsPromotion(object):
    NAME = 'ods_promotion'

    class Field:
        id = '_id'
        create_time = 'create_time'  # 创建时间
        has_fixed = 'has_fixed'  # 辅助字段
        import_update_time = 'import_update_time'  # 导入更新时间
        last_update_time = 'last_update_time'  # 最近更新时间
        name = 'name'  # 推广主体名称
        nameId = 'nameId'  # 主体映射id
        nameIdSource = 'nameIdSource'  # 映射源
        prmtDesc = 'prmtDesc'  # 推广描述
        prmtEmWords = 'prmtEmWords'  # 推广标题标红词
        prmtKey = 'prmtKey'  # 推广词
        prmtKeyword = 'prmtKeyword'  # 推广·关键词 已弃用
        prmtLink = 'prmtLink'  # 推广链接
        prmtLinkName = 'prmtLinkName'  # 推广链接名称
        prmtOriginLink = 'prmtOriginLink'  # 推广原始链接
        prmtRank = 'prmtRank'  # 推广主体评级
        prmtSnapshotList = 'prmtSnapshotList'  # 品专推广快照
        prmtSource = 'prmtSource'  # 推广来源
        prmtTime = 'prmtTime'  # 推广时间
        prmtTitle = 'prmtTitle'  # 推广标题
        prmtType = 'prmtType'  # 推广广告类型
        update_time = 'update_time'  # 更新时间


class DwmEnterprisePromotionLink(object):
    NAME = 'dwm_enterprise_promotion_link'

    class Field:
        id = '_id'
        nameId = 'nameId'
        lastPrmtTime = 'lastPrmtTime'
        prmtKeys = 'prmtKeys'
        prmtKeysNumber = 'prmtKeysNumber'
        prmtLink = 'prmtLink'
        type = 'type'


# ============================================================================
# MAIN APPLICATION CODE (original logic)
# ============================================================================

# Create aliases for easier reference (matching original imports)
DwmEPL = DwmEnterprisePromotionLink

# Job name construction
JOB_NAME = 'DW_{}_{}'.format(ModuleNameConstant.enterprise, DwmEnterprisePromotionLink.NAME)


def promotion_filter(promotion_dict):
    """
    过滤数据：缺少企业映射、缺少推广源、缺少推广关键词
    """
    for k in ['nameId', 'prmtLink']:
        if k not in promotion_dict:
            return False
    return True


def map_promotion_link(promotion_dict):
    name_id = promotion_dict[OdsPromotion.Field.nameId]
    key = promotion_dict[OdsPromotion.Field.prmtKey]
    time = promotion_dict.get(OdsPromotion.Field.prmtTime)
    link = promotion_dict[OdsPromotion.Field.prmtLink]

    name_link_data = {
        DwmEPL.Field.prmtKeys: [
            {
                OdsPromotion.Field.prmtTime: time,
                OdsPromotion.Field.prmtKey: key
            }
        ],
        DwmEPL.Field.lastPrmtTime: time,
    }

    return name_id + '_' + link, name_link_data


def reduce_promotion_link(x_dict, y_dict):
    # 合并推广关键词
    x_dict[DwmEPL.Field.prmtKeys] = x_dict[DwmEPL.Field.prmtKeys] + y_dict[DwmEPL.Field.prmtKeys]

    # 推广时间取更新的
    if x_dict[DwmEPL.Field.lastPrmtTime] < y_dict[DwmEPL.Field.lastPrmtTime]:
        x_dict[DwmEPL.Field.lastPrmtTime] = y_dict[DwmEPL.Field.lastPrmtTime]

    return x_dict


def map_format_promotion(data):
    key, promotion_link_dict = data
    words = key.split('_')
    name_id, link = words[0], '_'.join(words[1:])
    promotion_link_dict[DwmEPL.Field.id] = str_md5(key, digit=16)
    promotion_link_dict[DwmEPL.Field.nameId] = name_id
    promotion_link_dict[DwmEPL.Field.prmtLink] = link

    # 推广关键词按推广时间排序
    prmt_keys = promotion_link_dict[DwmEPL.Field.prmtKeys]
    prmt_keys = sorted(prmt_keys, key=lambda k: k[OdsPromotion.Field.prmtTime], reverse=True)

    # 去重
    unique_keys = list()
    key_set = set()
    for key_doc in prmt_keys:
        key = key_doc[OdsPromotion.Field.prmtKey]
        if key not in key_set:
            unique_keys.append(key)
            key_set.add(key)

    # 只需要前三个
    promotion_link_dict[DwmEPL.Field.prmtKeys] = unique_keys[0: 3]
    promotion_link_dict[DwmEPL.Field.prmtKeysNumber] = len(unique_keys)
    return promotion_link_dict


def run(*args):
    spark = SparkSession.builder.appName(JOB_NAME).getOrCreate()
    sc = spark.sparkContext
    ods_promotion_input, dwm_enterprise_promotion_link_output, n_partition = args
    n_partition = int(n_partition)
    ods_promotion_rdd: RDD = get_rdd_from_file(sc, ods_promotion_input)
    dwm_enterprise_promotion_link_rdd = ods_promotion_rdd.filter(
        promotion_filter
    ).map(
        map_promotion_link
    ).reduceByKey(
        reduce_promotion_link
    ).map(
        map_format_promotion
    )
    persist_rdd(dwm_enterprise_promotion_link_rdd, dwm_enterprise_promotion_link_output,
                numPartitions=n_partition)


if __name__ == '__main__':
    run(*sys.argv[1:])
