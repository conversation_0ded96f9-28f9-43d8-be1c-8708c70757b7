# =============================================================================
# JetBrains IDEs (PyCharm, IntelliJ IDEA, etc.)
# =============================================================================
.idea/
*.iml
*.ipr
*.iws
.idea_modules/
atlassian-ide-plugin.xml
com_crashlytics_export_strings.xml
crashlytics.properties
crashlytics-build.properties
fabric.properties

# =============================================================================
# Python
# =============================================================================
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# =============================================================================
# Apache Spark / PySpark
# =============================================================================
# Spark logs
spark-warehouse/
metastore_db/
derby.log
*.log

# Spark checkpoints
checkpoints/
checkpoint/

# Spark temporary files
spark-events/
.spark-warehouse/

# Hadoop logs
hadoop-logs/

# =============================================================================
# Big Data / Data Processing
# =============================================================================
# Data files (adjust based on your needs)
*.parquet
*.avro
*.orc
# Uncomment if you don't want to track large data files
# *.csv
# *.json
# *.bson
# *.jsonl

# Temporary data processing files
tmp/
temp/
staging/
output/
results/

# =============================================================================
# Database
# =============================================================================
# MongoDB
*.bson.backup
dump/

# SQLite
*.sqlite
*.sqlite3
*.db

# =============================================================================
# Operating System Files
# =============================================================================
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# Configuration and Environment Files
# =============================================================================
# Environment variables and secrets
.env.local
.env.development
.env.test
.env.production
.env.*.local
config.ini
config.yaml
config.yml
secrets.json
credentials.json
.secrets/

# Application specific config files
application.properties
application-*.properties
application.yml
application-*.yml

# =============================================================================
# Logs and Temporary Files
# =============================================================================
# Log files
*.log
logs/
log/

# Temporary files
*.tmp
*.temp
*.swp
*.swo
*~

# Backup files
*.bak
*.backup
*.old

# =============================================================================
# Development Tools
# =============================================================================
# Visual Studio Code
.vscode/
*.code-workspace

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~
.netrwhist

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =============================================================================
# Version Control
# =============================================================================
# Git
.git/
*.orig
*.rej

# =============================================================================
# Miscellaneous
# =============================================================================
# Compressed files
*.zip
*.tar.gz
*.rar
*.7z

# System files
.DS_Store?
ehthumbs.db
Icon?
Thumbs.db

# Node.js (in case of mixed projects)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Docker
.dockerignore
Dockerfile.local
docker-compose.override.yml
